from pathlib import Path
import sys

# Anchors used to heuristically identify the project root in development mode
_ANCHORS = [
    'pyproject.toml',
    'requirements.txt',
    'main.py',
    'constants.py',
]

def _find_root_from(start: Path) -> Path:
    """Search upwards from 'start' for a directory containing any anchor file.
    Fallback to the start directory if nothing found.
    """
    for parent in [start, *start.parents]:
        for anchor in _ANCHORS:
            if (parent / anchor).exists():
                return parent
    return start


def get_project_root() -> Path:
    """Return the project root directory.
    - If running as a frozen app (PyInstaller/EXE), use the executable directory
    - Else search upwards from this file's directory for an anchor
    """
    if getattr(sys, 'frozen', False):
        return Path(sys.executable).resolve().parent
    return _find_root_from(Path(__file__).resolve())


def get_log_path(filename: str) -> Path:
    """Return absolute path to a log file located at the project root."""
    return get_project_root() / filename

