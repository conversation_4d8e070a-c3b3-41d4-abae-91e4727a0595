# 视觉检测操作指南 v1.0

## 📋 目录
1. [系统启动](#系统启动)
2. [摄像头设置模式](#摄像头设置模式)
3. [校准模式](#校准模式)
4. [检测模式](#检测模式)
5. [ROI微调操作](#roi微调操作)
6. [特殊功能](#特殊功能)
7. [故障排除](#故障排除)

---

## 🚀 系统启动

### 启动程序
```bash
python main.py
```

### 程序启动后状态
- 默认进入**摄像头设置模式**
- 窗口标题：`LED & Digit Detector - Press 'h' for help v1.5`
- 窗口大小：960x540（可调整）

### 全局快捷键（任何模式下都可用）
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Q` | 退出程序 | 安全退出，自动保存配置 |
| `H` | 显示帮助 | 显示当前模式的快捷键说明 |

---

## 📷 摄像头设置模式

### 模式说明
- 用于调整摄像头参数：分辨率、曝光、亮度
- 确保图像质量满足检测要求
- 完成设置后进入校准模式

### 快捷键操作

#### 分辨率调整
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `T` | 提高分辨率 | 循环切换：640x480 → 800x600 → 1280x720 → 1920x1080 → 2560x1440 |
| `t` | 降低分辨率 | 反向循环切换分辨率 |

#### 曝光调整
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `E` | 增加曝光 | 每次增加1.0，适用于光线较暗环境 |
| `e` | 减少曝光 | 每次减少1.0，适用于光线较亮环境 |

#### 亮度调整
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `B` | 增加亮度 | 每次增加10.0 |
| `b` | 减少亮度 | 每次减少10.0 |

#### 模式切换
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `S` | 保存设置 | 保存当前摄像头参数到配置文件 |
| `Enter` | 进入校准模式 | 确认摄像头设置，开始校准流程 |

### 操作步骤
1. **调整分辨率**：按 `T`/`t` 选择合适的分辨率（推荐1280x720）
2. **调整曝光**：按 `E`/`e` 调整曝光值，确保LED和数码管清晰可见
3. **调整亮度**：按 `B`/`b` 微调亮度，优化图像对比度
4. **保存设置**：按 `S` 保存当前参数
5. **确认设置**：按 `Enter` 进入校准模式

---

## 🎯 校准模式

### 模式说明
校准模式是系统的核心配置阶段，包含以下子流程：
1. 基准点选择（可选）
2. LED ROI选择和样本采集
3. 数码管ROI选择和配置
4. 阈值分析和优化

### 校准主菜单

#### 主要功能快捷键
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `B` | 基准点校准 | 选择2个基准点用于动态位置校正 |
| `L` | LED校准 | 开始LED检测区域选择和样本采集 |
| `R` | 快速重新采样 | 保留ROI坐标，只重新采集LED样本 |
| `E` | 编辑LED ROI | 进入LED ROI编辑模式 |
| `D` | 数码管校准 | 开始数码管检测配置 |
| `S` | 保存并退出 | 保存所有配置并进入检测模式 |
| `Enter` | 进入检测模式 | 直接进入检测（需要校准完整） |

---

### 🎯 基准点校准

#### 操作说明
基准点用于动态校正设备位移，提高检测稳定性。

#### 操作步骤
1. **进入基准点模式**：在校准主菜单按 `B`
2. **选择第一个基准点**：
   - 在图像上点击明显的特征点（如螺丝孔、标记、边角）
   - 系统自动提取该点的模板特征
3. **选择第二个基准点**：
   - 点击另一个稳定的特征点
   - 两个基准点应有一定距离，便于计算位移

#### 快捷键操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Enter` | 确认基准点 | 完成基准点选择，继续LED校准 |
| `R` | 重新选择 | 清除已选基准点，重新开始 |
| `Esc` | 跳过基准点 | 禁用动态对齐，使用固定坐标 |

---

### 💡 LED校准

#### 操作说明
LED校准包括ROI选择、样本采集、阈值分析三个阶段。

#### 阶段1：LED ROI选择

##### 操作步骤
1. **进入LED校准**：在校准主菜单按 `L`
2. **选择LED区域**：
   - 用鼠标拖拽选择每个LED的检测区域
   - 系统按顺序提示选择：G1, G2, ..., G33, R1, R2
   - 拖拽时显示蓝色矩形框

##### 快捷键操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Enter` | 确认当前ROI | 确认选择的区域，进入下一个LED |
| `N` | 跳过当前LED | 不设置该LED的检测区域 |
| `B` | 回退上一个 | 返回上一个LED重新选择 |
| `T` | 模板复制模式 | 使用前一个ROI作为模板快速复制 |
| `A` | 固定尺寸模板 | 使用固定大小模板快速选择 |
| `R` | 重置所有ROI | 清除所有已选择的ROI，重新开始 |
| `Esc` | 退出/取消模板 | 退出当前操作或取消模板模式 |

##### 模板复制功能
- **模板复制模式**：按 `T` 激活，鼠标点击位置自动创建相同尺寸的ROI
- **固定尺寸模板**：按 `A` 激活，使用预设尺寸（默认12x12像素）
- **退出模板模式**：按 `Esc` 或 `B` 退出模板模式

#### 阶段2：LED样本采集

##### 采集关闭状态样本
1. **确保所有LED处于关闭状态**
2. **开始采集**：按 `C` 开始采集关闭状态样本
3. **等待完成**：系统自动采集10帧样本数据

##### 采集开启状态样本
1. **确保所有LED处于开启状态**
2. **开始采集**：按 `C` 开始采集开启状态样本
3. **等待完成**：系统自动采集10帧样本数据

##### 快捷键操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `C` | 开始采集 | 采集当前状态的样本数据 |
| `Esc` | 返回上一步 | 返回ROI选择或样本采集阶段 |

#### 阶段3：LED阈值分析

##### 操作步骤
1. **开始分析**：按 `A` 开始自动分析最优阈值
2. **查看结果**：系统显示计算出的阈值参数
3. **确认完成**：自动返回校准主菜单

##### 快捷键操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `A` | 开始分析 | 计算最优检测阈值 |
| `Esc` | 返回采样 | 返回样本采集阶段 |

---

### 📝 数码管校准

#### 操作说明
数码管校准包括捕获"88"图像、选择数码管ROI、选择段ROI、背景采集、阈值调整。

#### 阶段1：捕获"88"图像
1. **显示"88"**：确保数码管显示"88"（所有段点亮）
2. **捕获图像**：按 `C` 捕获当前图像作为校准参考

#### 阶段2：选择数码管ROI
1. **选择左侧数码管**：拖拽选择第一个数码管区域
2. **确认选择**：按 `Enter` 确认
3. **选择右侧数码管**：拖拽选择第二个数码管区域
4. **确认选择**：按 `Enter` 确认

#### 阶段3：选择段ROI
1. **按顺序选择段**：系统提示选择每个段（a, b, c, d, e, f, g）
2. **拖拽选择**：为每个段选择检测区域
3. **确认或跳过**：按 `Enter` 确认，按 `N` 跳过该段

#### 阶段4：背景采集
1. **关闭所有段**：确保数码管完全关闭
2. **捕获背景**：按 `C` 捕获背景图像

#### 阶段5：阈值调整
1. **调整阈值**：使用 `+`/`-` 调整亮度阈值
2. **实时预览**：观察检测效果
3. **确认设置**：按 `Enter` 完成数码管校准

#### 数码管校准快捷键
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `C` | 捕获图像 | 捕获"88"图像或背景图像 |
| `Enter` | 确认选择 | 确认当前ROI或设置 |
| `N` | 跳过段 | 跳过当前段的选择 |
| `P` | 上一个段 | 返回上一个段重新选择 |
| `+`/`=` | 增加阈值 | 提高亮度检测阈值 |
| `-`/`_` | 减少阈值 | 降低亮度检测阈值 |
| `Esc` | 返回上一步 | 返回前一个操作阶段 |

---

## 🔍 检测模式

### 模式说明
检测模式是系统的工作状态，实时检测LED状态和数码管字符。

### 快捷键操作

#### 模式切换
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `C` | 进入校准模式 | 重新校准系统参数 |
| `W` | 窗口置顶切换 | 切换窗口置顶状态 |

#### 配置保存
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `S` | 保存完整配置 | 保存所有参数到配置文件 |
| `L` | 快速保存LED参数 | 仅保存LED样本和阈值 |

#### 功能控制
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `B` | 切换基准点对齐 | 启用/禁用动态位置校正 |

#### 实时阈值调整

##### LED阈值调整
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `G` | 减少绿色LED灰度阈值 | 每次减少5.0 |
| `g` | 增加绿色LED灰度阈值 | 每次增加5.0 |
| `V` | 减少绿色LED绿色通道阈值 | 每次减少5.0 |
| `v` | 增加绿色LED绿色通道阈值 | 每次增加5.0 |
| `Y` | 减少红色LED灰度阈值 | 每次减少5.0 |
| `y` | 增加红色LED灰度阈值 | 每次增加5.0 |
| `R` | 减少红色LED红色通道阈值 | 每次减少5.0 |
| `r` | 增加红色LED红色通道阈值 | 每次增加5.0 |

##### G33特殊LED阈值调整
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `3` | 减少G33灰度阈值 | 每次减少5.0 |
| `#` | 增加G33灰度阈值 | 每次增加5.0 |
| `e` | 减少G33绿色通道阈值 | 每次减少5.0 |
| `E` | 增加G33绿色通道阈值 | 每次增加5.0 |

##### 数码管阈值调整
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `+`/`=` | 增加数码管亮度阈值 | 每次增加1.0 |
| `-`/`_` | 减少数码管亮度阈值 | 每次减少1.0 |

---

## 🎛️ ROI微调操作

### 功能说明
在ROI选择过程中，可以使用精细调整功能精确定位检测区域。

### 激活条件
- 在任何ROI选择界面（LED ROI、数码管ROI、段ROI）
- 先拖拽出一个矩形（出现蓝框）
- 然后可以使用微调快捷键

### 位置调整
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `W` | 上移 | 向上移动当前步长像素 |
| `A` | 左移 | 向左移动当前步长像素 |
| `S` | 下移 | 向下移动当前步长像素 |
| `D` | 右移 | 向右移动当前步长像素 |

### 尺寸调整
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Shift+W` | 缩小高度 | 减少当前步长像素高度 |
| `Shift+A` | 缩小宽度 | 减少当前步长像素宽度 |
| `Shift+S` | 增大高度 | 增加当前步长像素高度 |
| `Shift+D` | 增大宽度 | 增加当前步长像素宽度 |

### 步长调整
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `1`-`9` | 设置步长 | 设置微调步长为1-9像素 |

### 微调显示
- 界面右上角显示当前步长
- 控制台实时打印微调反馈（如"微调: 左移 1px"）

---

## 🔧 特殊功能

### LED ROI编辑模式

#### 进入方式
在校准主菜单按 `E` 进入LED ROI编辑模式

#### 操作方式
1. **选择ROI**：点击要编辑的ROI或按数字键选择
2. **移动ROI**：拖拽ROI到新位置
3. **微调位置**：使用WASD键精细调整
4. **确认更改**：按 `Enter` 保存所有更改

#### 快捷键
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Enter` | 保存更改 | 确认所有ROI位置更改 |
| `Esc` | 退出编辑 | 取消更改，返回校准主菜单 |

### 窗口置顶功能

#### 操作方式
在检测模式下按 `W` 切换窗口置顶状态

#### 功能说明
- 支持Windows API和OpenCV双重实现
- 自动检测窗口标题进行置顶设置
- 状态变化会在界面显示反馈

---

## 🚨 故障排除

### 常见问题及解决方案

#### 摄像头问题
**问题**：摄像头无法初始化
**解决方案**：
1. 检查USB连接
2. 确认摄像头驱动正常
3. 尝试更换摄像头索引（修改配置文件中的camera_index）

#### 检测精度问题
**问题**：LED检测不准确
**解决方案**：
1. 重新校准ROI位置
2. 调整检测阈值（使用实时调整快捷键）
3. 重新采集样本数据（使用快速重新采样功能）
4. 检查光照条件是否稳定

#### 界面响应问题
**问题**：界面卡顿或无响应
**解决方案**：
1. 检查CPU使用率
2. 降低摄像头分辨率
3. 关闭不必要的后台程序
4. 重启程序

#### 配置丢失问题
**问题**：配置参数丢失
**解决方案**：
1. 检查combined_config.json文件是否存在
2. 重新进行完整校准流程
3. 确保有足够的磁盘空间保存配置

### 日志查看
- 查看 `led_digit_detection.log` 文件了解详细运行信息
- 控制台输出提供实时操作反馈
- WebSocket连接状态可通过网页客户端查看

---

## 📝 操作流程总结

### 完整操作流程
1. **启动程序** → `python main.py`
2. **摄像头设置** → 调整分辨率、曝光、亮度 → 按 `Enter`
3. **基准点校准** → 按 `B` → 选择2个基准点 → 按 `Enter`
4. **LED校准** → 按 `L` → 选择ROI → 采集样本 → 分析阈值
5. **数码管校准** → 按 `D` → 捕获"88" → 选择ROI → 调整阈值
6. **保存配置** → 按 `S` → 进入检测模式
7. **实时检测** → 监控结果 → 必要时调整阈值

### 快速重新校准流程
1. **进入校准模式** → 按 `C`
2. **快速重新采样** → 按 `R` → 重新采集LED样本
3. **保存配置** → 按 `S` → 返回检测模式

---

*最后更新：2025-08-28*
*版本：v1.0*
