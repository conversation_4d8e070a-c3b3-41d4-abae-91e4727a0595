"""
幽灵窗口测试脚本
用于验证2x2像素幽灵窗口的功能
"""
import sys
import time
import logging
import cv2
import numpy as np
import threading

# 添加项目路径
sys.path.append('.')
from ghost_window import GhostWindow

def test_ghost_window():
    """测试幽灵窗口功能"""
    print("开始测试幽灵窗口...")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建幽灵窗口
    ghost_win = GhostWindow("TestGhostWindow")
    
    try:
        # 启动幽灵窗口
        ghost_win.start()
        print("幽灵窗口已启动，请在屏幕上查找2x2像素的小窗口")
        
        # 等待用户确认
        print("窗口将运行10秒钟，请观察...")
        
        # 测试颜色变化
        colors = [
            (128, 128, 128),  # 灰色
            (0, 0, 255),      # 红色
            (0, 255, 0),      # 绿色
            (255, 0, 0),      # 蓝色
            (255, 255, 0),    # 青色
            (255, 0, 255),    # 品红
            (0, 255, 255),    # 黄色
            (255, 255, 255)   # 白色
        ]
        
        for i, color in enumerate(colors):
            print(f"  变更颜色 {i+1}/8: {color}")
            ghost_win.set_color(color)
            time.sleep(1.2)
        
        # 测试位置变化
        positions = [(50, 50), (100, 100), (200, 200), (300, 300)]
        for i, pos in enumerate(positions):
            print(f"  移动位置 {i+1}/4: {pos}")
            ghost_win.set_position(pos[0], pos[1])
            time.sleep(1)
        
        print("测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        logging.error(f"幽灵窗口测试失败: {e}")
    
    finally:
        # 清理
        ghost_win.stop()
        print("幽灵窗口已关闭")

def test_ghost_window_with_main_loop():
    """测试带有主循环的幽灵窗口功能"""
    print("\n测试带主循环的幽灵窗口...")
    
    ghost_win = GhostWindow("MainLoopTest")
    
    try:
        ghost_win.start()
        print("幽灵窗口已启动")
        print("主循环将运行10秒，期间会定期更新幽灵窗口...")
        
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 10:
            # 在主线程中更新幽灵窗口
            ghost_win.update_in_main_thread()
            
            # 处理按键
            key = cv2.waitKey(100) & 0xFF
            if key == ord('q'):
                print("用户中断测试")
                break
            
            frame_count += 1
            
            # 每2秒改变一次颜色
            if frame_count % 20 == 0:
                color = np.random.randint(0, 255, 3).tolist()
                ghost_win.set_color(color)
                print(f"  颜色变更为: {color}")
        
        print(f"主循环结束，共处理了 {frame_count} 帧")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        logging.error(f"幽灵窗口主循环测试失败: {e}")
    
    finally:
        ghost_win.stop()
        print("幽灵窗口已关闭")

def test_visibility_check():
    """测试可见性检查功能"""
    print("\n测试可见性检查...")
    
    ghost_win = GhostWindow("VisibilityTest")
    
    try:
        ghost_win.start()
        time.sleep(1)
        
        # 检查可见性
        visible = ghost_win.is_visible()
        print(f"幽灵窗口可见性: {visible}")
        
        # 最小化测试（如果有其他窗口）
        print("尝试最小化其他窗口，观察幽灵窗口是否仍然可见")
        
        # 运行主循环几秒钟
        for i in range(30):
            ghost_win.update_in_main_thread()
            cv2.waitKey(100)
            if i % 10 == 0:
                print(f"  {i//10}/3 秒...")
        
    finally:
        ghost_win.stop()

if __name__ == "__main__":
    print("=== 幽灵窗口功能测试 ===")
    print("这个测试将创建一个2x2像素的小窗口")
    print("即使主程序窗口最小化，这个窗口也会保持可见")
    print()
    
    # 运行测试
    test_ghost_window()
    test_ghost_window_with_main_loop()
    test_visibility_check()
    
    print("\n=== 测试完成 ===")
    print("如果幽灵窗口正常工作，您应该看到一个2x2像素的小窗口")
    print("它会改变颜色和位置，并且在主窗口最小化时仍然可见")
    print("最重要的是，它不会影响主UI界面的正常显示")