# 配置文件增量更新功能使用说明

## 功能概述

新增的增量更新功能允许您在产品位置和相机位置固定的情况下，只更新LED样本数据和阈值，而保留ROI坐标等其他配置不变。这样可以避免重新校准ROI坐标的繁琐过程。

## 主要特性

### 1. 快速重新采样模式
- **保留**：ROI坐标、相机设置、基准点配置等
- **更新**：LED亮灭样本数据、LED阈值
- **优势**：无需重新选择ROI，快速适应光照变化

### 2. 增量保存功能
- **LED样本和阈值增量保存**：只更新LED相关的样本和阈值数据
- **数码管保持原有机制**：使用+/-键实时调整，S键完整保存
- **安全机制**：自动备份、原子写入、错误恢复

## 使用方法

### 方法一：校准模式下的快速重新采样

1. **进入校准模式**：在检测模式下按 `C` 键
2. **选择快速重新采样**：在校准主菜单按 `R` 键
   ```
   Calibration: 'B' Base Points | 'L' LED | 'D' Digit | 'E' Edit LEDs | 'R' Resample LEDs | 'S' Save&Exit Calib | 'Enter' Detect (if ready)
   ```
3. **采集灭灯样本**：确保LED处于灭灯状态，按 `C` 键采集
4. **采集亮灯样本**：确保LED处于亮灯状态，按 `C` 键采集
5. **分析和保存**：按 `A` 键分析样本并计算阈值
6. **自动增量保存**：系统会自动使用增量保存，只更新样本和阈值

### 方法二：检测模式下的快速保存

在检测模式下，如果您已经通过其他方式更新了阈值（如实时调整），可以使用：
- **`S` 键**：完整保存所有配置（包括LED和数码管）
- **`L` 键**：快速保存LED样本和阈值（新增功能，不保存数码管）

### 数码管的调整和保存
数码管保持原有的简单稳定机制：
- **实时调整**：`+/-` 键调整亮度阈值，立即看到效果
- **保存**：`S` 键完整保存所有配置

## 技术实现

### 新增函数

#### `save_led_samples_and_thresholds_only(app_state)`
```python
# 仅更新LED样本数据和阈值，保留ROI坐标等其他配置不变
config_manager.save_led_samples_and_thresholds_only(app_state)
```

**更新的字段**：
- `led_settings.off_samples`
- `led_settings.on_samples`
- `led_settings.gray_threshold_green`
- `led_settings.green_threshold`
- `led_settings.gray_threshold_red`
- `led_settings.red_threshold`
- `timestamp`

**保持不变的字段**：
- `led_settings.rois`（ROI坐标）
- `led_settings.num_green/num_red`
- `camera_settings`
- `digit_settings`
- `base_alignment`
- 其他所有配置

#### `save_digit_threshold_only(app_state)`
```python
# 仅更新数码管亮度阈值
config_manager.save_digit_threshold_only(app_state)
```

**更新的字段**：
- `digit_settings.brightness_threshold`
- `timestamp`

### 安全机制

1. **自动备份**：更新前自动创建 `.bak` 备份文件
2. **原子写入**：使用临时文件+原子替换，防止写入过程中断电导致文件损坏
3. **错误恢复**：更新失败时自动从备份恢复
4. **前置检查**：确保配置文件存在才能进行增量更新

## 使用场景

### 适合使用增量更新的情况：
- ✅ 产品位置固定，相机位置固定
- ✅ ROI坐标已经校准好，只需要调整检测参数
- ✅ 光照条件发生变化，需要重新采集样本
- ✅ 需要微调阈值参数
- ✅ 多班次生产，每班次光照略有差异

### 仍需完整校准的情况：
- ❌ 产品位置发生变化
- ❌ 相机位置或角度调整
- ❌ LED数量发生变化
- ❌ 需要重新选择ROI区域
- ❌ 首次设置系统

## 操作流程对比

### 传统完整校准流程：
1. 进入校准模式 → 2. 选择LED校准 → 3. 逐个选择ROI → 4. 采集灭灯样本 → 5. 采集亮灯样本 → 6. 分析计算阈值 → 7. 保存配置

**耗时**：约5-10分钟（取决于LED数量）

### 新增快速重新采样流程：
1. 进入校准模式 → 2. 选择快速重新采样 → 3. 采集灭灯样本 → 4. 采集亮灯样本 → 5. 分析计算阈值 → 6. 自动增量保存

**耗时**：约1-2分钟

## 注意事项

1. **配置文件完整性**：增量更新需要现有的完整配置文件，首次使用仍需完整校准
2. **备份管理**：系统会自动创建 `.bak` 备份，建议定期清理旧备份
3. **版本兼容性**：增量更新保持配置文件版本号不变，确保兼容性
4. **错误处理**：如果增量更新失败，系统会自动回退到完整保存模式

## 测试验证

运行测试脚本验证功能：
```bash
python test_config_update.py
```

测试内容包括：
- 增量更新功能正确性
- ROI坐标保持不变
- 样本和阈值正确更新
- 其他配置保持不变
- 备份和恢复机制

## 总结

增量更新功能显著提高了系统的易用性和效率，特别适合生产环境中的快速调整需求。通过保留稳定的ROI配置，只更新变化的检测参数，大大减少了重新校准的时间和工作量。





对话总结:
---------01：
让我仔细检查一下当前检测模式下的保存逻辑：

现在让我检查 `save_led_samples_and_thresholds_only()` 函数具体保存了什么：

## 您的理解完全正确！

### 检测模式下的保存机制：

#### `S` 键 - 完整保存
- 调用：`save_config(app_state)`
- 保存内容：**所有配置**
  - LED设置（ROI坐标、样本数据、阈值）
  - 数码管设置（ROI坐标、阈值）
  - 相机设置
  - 基准点配置
  - 等等所有配置

#### `Q` 键 - LED专用快速保存
- 调用：`save_led_samples_and_thresholds_only(app_state)`
- 保存内容：**只有LED相关**
  - `led_settings.off_samples`
  - `led_settings.on_samples`
  - `led_settings.gray_threshold_green`
  - `led_settings.green_threshold`
  - `led_settings.gray_threshold_red`
  - `led_settings.red_threshold`
  - `timestamp`

- **不保存**：
  - ❌ 数码管阈值（`digit_settings.brightness_threshold`）
  - ❌ LED ROI坐标
  - ❌ 数码管ROI坐标
  - ❌ 相机设置
  - ❌ 其他任何配置

### 总结

您的理解完全正确：

- **`S` 键**：保存所有更改（包含LED + 数码管 + 其他所有配置）
- **`Q` 键**：只保存LED的样本和阈值，数码管的阈值**不会保存**

如果用户在检测模式下调整了数码管阈值（用`+/-`键），然后按`Q`键，数码管的阈值调整会丢失，只有LED的更改被保存。



-------02：
您说得对！我需要仔细检查一下检测模式下的按键定义。让我查看检测模式的按键处理：

您完全正确！我犯了一个严重的错误。在检测模式下：

- **`Q` 键**：`app_state.running = False` - **退出程序**
- 我添加的 `Q` 键快速保存功能会与退出功能冲突！

让我检查我之前添加的代码：

问题很严重！我添加了重复的 `elif key == ord('q'):` 处理，这会导致：

1. **第一个 `q` 键处理**（1468行）：退出程序
2. **第二个 `q` 键处理**（1485行）：快速保存LED数据

由于第一个会先执行，所以第二个永远不会被执行到，而且这会导致逻辑混乱。

我需要立即修复这个问题。让我移除错误的重复代码，并选择一个不冲突的按键：

现在我需要更新底部提示信息，修正按键说明：

现在更新使用说明文档：

## 问题已修复！

感谢您的细心发现！我犯了一个严重的错误：

### ❌ 之前的错误
- 在检测模式下添加了重复的 `Q` 键处理
- `Q` 键本来是**退出程序**的功能
- 这会导致按键冲突和逻辑混乱

### ✅ 现在的修复
- **`Q` 键**：保持原有功能 - **退出程序**
- **`L` 键**：新增功能 - **快速保存LED样本和阈值**

### 当前检测模式下的按键布局
- **`Q` 键**：退出程序
- **`C` 键**：进入校准模式
- **`S` 键**：完整保存所有配置
- **`L` 键**：快速保存LED样本和阈值（新增）
- **`B` 键**：切换基准点对齐
- **`+/-` 键**：调整数码管阈值
- **其他键**：调整LED阈值

这样就不会有按键冲突了，谢谢您的提醒！`L` 键（LED的首字母）用来快速保存LED数据也很直观。

