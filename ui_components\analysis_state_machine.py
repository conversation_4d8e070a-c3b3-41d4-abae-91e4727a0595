"""
AnalysisStateMachine - encapsulates the full '88' analysis workflow state machine.
This module moves the complex stateful logic out of ui_modes, improving readability
and testability while preserving exact behavior and log messages.
"""
from __future__ import annotations

import os
import sys
import time
import logging
from typing import Callable

from constants import LOG_FILE
import async_task_manager
from async_task_manager import TaskType, TaskStatus
from utils.paths import get_log_path

class AnalysisStateMachine:
    """Encapsulates the '88' logging/analysis/CPU signaling workflow.

    Public API:
      - tick(app_state, current_display): call once per frame to advance the workflow
    """

    def __init__(self,
                 get_log_path_fn: Callable[[str], str] | None = None):
        # Allow injection for tests; default to utils.paths.get_log_path
        self.get_log_path_fn = get_log_path_fn or (lambda name: str(get_log_path(name)))

    def tick(self, app_state, current_display: str):
        """Advance the analysis workflow by one frame based on app_state.
        This is a direct extraction of the logic previously in ui_modes._run_detection_mode,
        with log texts kept intact.
        """
        logger = logging.getLogger()
        current_time = time.time()

        # --- IDLE State: 等待触发 ---
        if app_state.led_analysis_state == 'IDLE':
            # 检查 "88" 触发
            if current_display == "88":
                if app_state.log_file_handler:
                    logging.info(f"Detected '88', starting {app_state.logging_duration}s logging process.")
                    try:
                        # 清空旧日志文件（开始新一轮记录前）
                        with open(LOG_FILE, 'w', encoding='utf-8') as f:
                            pass
                        logging.info(f"Log file '{LOG_FILE}' cleared before starting.")
                        logger.addHandler(app_state.log_file_handler) # 激活日志记录
                        logging.info("FileHandler added to logger.") # 添加 handler 日志
                        app_state.logging_start_time = current_time
                        app_state.led_analysis_state = 'LOGGING'
                    except OSError as e:
                        print(f"Error clearing or starting log file: {e}")
                        logging.error(f"Error clearing or starting log file: {e}")
                    except Exception as e:
                        print(f"Error adding log handler: {e}")
                        logging.error(f"Error adding log handler: {e}")
                else:
                    print("Error: Log file handler not initialized.")
                    logging.error("Cannot start logging for '88': Log file handler not initialized.")

            # 检查IP/1P触发 - 同时检测多种可能的组合
            elif current_display in ["IP", "1P", "iP", "Ip"]:
                trigger_text = current_display
                logging.info(f"Detected '{trigger_text}', submitting async signal to CPU address 11.")

                # 异步发送IP信号
                task_manager = async_task_manager.get_task_manager()
                task_id = task_manager.submit_task(
                    TaskType.SEND_CPU_SIGNAL,
                    {'value': 1, 'address': 11}
                )
                logging.info(f"Submitted IP signal task: {task_id}")
                # 保持 IDLE

        # --- LOGGING State: 记录日志 ---
        elif app_state.led_analysis_state == 'LOGGING':
            elapsed_time = current_time - app_state.logging_start_time
            app_state.status_message = f"Logging for '88' analysis... {elapsed_time:.1f}s / {app_state.logging_duration}s"
            if elapsed_time >= app_state.logging_duration:
                logging.info(f"{app_state.logging_duration}s logging finished. Stopping file handler.")
                if app_state.log_file_handler:
                    try:
                        logger.removeHandler(app_state.log_file_handler)
                        logging.info("FileHandler removed from logger.") # 添加 handler 日志
                    except Exception as e:
                        print(f"Error removing log handler: {e}")
                        logging.error(f"Error removing log handler: {e}")
                app_state.led_analysis_state = 'WAITING_TO_SIGNAL_12'

        # --- WAITING_TO_SIGNAL_12 State: 异步发送日志完成信号 ---
        elif app_state.led_analysis_state == 'WAITING_TO_SIGNAL_12':
            if app_state.current_cpu_task_id is None:
                logging.info("Submitting CPU task: send 1 to addr 12")
                task_manager = async_task_manager.get_task_manager()
                task_id = task_manager.submit_task(
                    TaskType.SEND_CPU_SIGNAL,
                    {'value': 1, 'address': 12}
                )
                app_state.current_cpu_task_id = task_id

            task_manager = async_task_manager.get_task_manager()
            task_status = task_manager.get_task_status(app_state.current_cpu_task_id)

            if task_status == TaskStatus.RUNNING:
                app_state.status_message = "Sending log completion signal to CPU..."

            elif task_status == TaskStatus.COMPLETED:
                result = task_manager.get_task_result(app_state.current_cpu_task_id)
                if result:
                    app_state.led_analysis_state = 'ANALYZING'
                    logging.info("Signal 1 to addr 12 sent successfully. Starting analysis.")
                    app_state.current_cpu_task_id = None
                    app_state.async_task_progress = ""
                else:
                    logging.error("Failed to send signal (1 to addr 12). Retrying.")
                    app_state.current_cpu_task_id = None

            elif task_status == TaskStatus.FAILED:
                error_msg = task_manager.get_task_error(app_state.current_cpu_task_id)
                logging.error(f"CPU communication task failed: {error_msg}")
                app_state.current_cpu_task_id = None

        # --- ANALYZING State: 异步执行分析脚本 ---
        elif app_state.led_analysis_state == 'ANALYZING':
            if app_state.current_analysis_task_id is None:
                try:
                    log_file_path = self.get_log_path_fn(LOG_FILE)
                    logging.info(f"Analysis log file path: {log_file_path}")
                    try:
                        import os
                        logging.info(f"Log file exists: {os.path.exists(log_file_path)}")
                        if os.path.exists(log_file_path):
                            file_size = os.path.getsize(log_file_path)
                            logging.info(f"Log file size: {file_size} bytes")
                    except Exception:
                        pass

                    task_manager = async_task_manager.get_task_manager()
                    task_id = task_manager.submit_task(
                        TaskType.ANALYZE_LOG,
                        {'log_file_path': log_file_path}
                    )
                    app_state.current_analysis_task_id = task_id
                    app_state.async_task_progress = "Analyzing log file..."
                    logging.info(f"Submitted analysis task: {task_id}")
                except Exception as e:
                    logging.error(f"Error submitting analysis task: {e}")
                    app_state.led_analysis_state = 'CLEARING'
                    return

            task_manager = async_task_manager.get_task_manager()
            task_status = task_manager.get_task_status(app_state.current_analysis_task_id)

            if task_status == TaskStatus.RUNNING:
                app_state.status_message = "Analyzing log file, please wait..."
                app_state.async_task_progress = "Analysis in progress..."

            elif task_status == TaskStatus.COMPLETED:
                analysis_result = task_manager.get_task_result(app_state.current_analysis_task_id)
                if analysis_result:
                    found_cycles = analysis_result['perfect_cycles']
                    special_leds_status = analysis_result['special_leds_status']
                    app_state.last_analysis_result = found_cycles
                    app_state.last_special_leds_result = special_leds_status
                    logging.info(f"Analysis complete. Found {found_cycles} perfect cycles.")
                    logging.info(f"Special LEDs (G33/R1/R2) status: {special_leds_status}")

                    g33_result = analysis_result['g33_result']
                    r1_result = analysis_result['r1_result']
                    r2_result = analysis_result['r2_result']
                    logging.info(f"G33: {g33_result['total_duration']:.3f}s ({'GOOD' if g33_result['is_good'] else 'BAD'})")
                    logging.info(f"R1: {r1_result['total_duration']:.3f}s ({'GOOD' if r1_result['is_good'] else 'BAD'})")
                    logging.info(f"R2: {r2_result['total_duration']:.3f}s ({'GOOD' if r2_result['is_good'] else 'BAD'})")

                    app_state.led_analysis_state = 'SENDING_RESULT_10'
                    app_state.current_analysis_task_id = None
                    app_state.async_task_progress = ""
                else:
                    logging.error("Analysis function returned None or empty result.")
                    app_state.led_analysis_state = 'CLEARING'
                    app_state.current_analysis_task_id = None
                    app_state.async_task_progress = ""

            elif task_status == TaskStatus.FAILED:
                error_msg = task_manager.get_task_error(app_state.current_analysis_task_id)
                logging.error(f"Analysis task failed: {error_msg}")
                app_state.led_analysis_state = 'CLEARING'
                app_state.current_analysis_task_id = None
                app_state.async_task_progress = ""

        # --- SENDING_RESULT_10 State: 异步发送分析结果信号 ---
        elif app_state.led_analysis_state == 'SENDING_RESULT_10':
            if app_state.last_analysis_result is not None:
                if app_state.current_cpu_task_id is None:
                    value_to_send = 3 if app_state.last_analysis_result == 0 else 1
                    logging.info(f"SENDING_RESULT_10: last_analysis_result={app_state.last_analysis_result}, will send {value_to_send} to addr 10")
                    logging.info(f"Submitting CPU task: send {value_to_send} to addr 10")
                    task_manager = async_task_manager.get_task_manager()
                    task_id = task_manager.submit_task(
                        TaskType.SEND_CPU_SIGNAL,
                        {'value': value_to_send, 'address': 10}
                    )
                    app_state.current_cpu_task_id = task_id

                task_manager = async_task_manager.get_task_manager()
                task_status = task_manager.get_task_status(app_state.current_cpu_task_id)

                if task_status == TaskStatus.RUNNING:
                    app_state.status_message = "Sending analysis result to CPU..."

                elif task_status == TaskStatus.COMPLETED:
                    result = task_manager.get_task_result(app_state.current_cpu_task_id)
                    if result:
                        app_state.led_analysis_state = 'SENDING_RESULT_13'
                        logging.info("Signal to addr 10 sent successfully. Moving to send M13.")
                        app_state.current_cpu_task_id = None
                        app_state.async_task_progress = ""
                    else:
                        logging.error("Failed to send result to addr 10. Retrying.")
                        app_state.current_cpu_task_id = None

                elif task_status == TaskStatus.FAILED:
                    error_msg = task_manager.get_task_error(app_state.current_cpu_task_id)
                    logging.error(f"CPU communication task failed: {error_msg}")
                    app_state.current_cpu_task_id = None
            else:
                logging.warning("No analysis result found, skipping sending to address 10.")
                app_state.led_analysis_state = 'CLEARING'

        # --- SENDING_RESULT_13 State: 异步发送G33/R1/R2监控结果 ---
        elif app_state.led_analysis_state == 'SENDING_RESULT_13':
            if app_state.last_special_leds_result is not None:
                if app_state.current_cpu_task_id is None:
                    value_to_send = 1 if app_state.last_special_leds_result == 'GOOD' else 3
                    logging.info(f"SENDING_RESULT_13: last_special_leds_result={app_state.last_special_leds_result}, will send {value_to_send} to addr 13")
                    logging.info(f"Submitting CPU task: send {value_to_send} to addr 13. Status: {app_state.last_special_leds_result}")
                    task_manager = async_task_manager.get_task_manager()
                    task_id = task_manager.submit_task(
                        TaskType.SEND_CPU_SIGNAL,
                        {'value': value_to_send, 'address': 13}
                    )
                    app_state.current_cpu_task_id = task_id

                task_manager = async_task_manager.get_task_manager()
                task_status = task_manager.get_task_status(app_state.current_cpu_task_id)

                if task_status == TaskStatus.RUNNING:
                    app_state.status_message = "Sending special LED result to CPU..."

                elif task_status == TaskStatus.COMPLETED:
                    result = task_manager.get_task_result(app_state.current_cpu_task_id)
                    if result:
                        app_state.led_analysis_state = 'CLEARING'
                        logging.info("Signal to addr 13 sent successfully.")
                        app_state.current_cpu_task_id = None
                        app_state.async_task_progress = ""
                    else:
                        logging.error("Failed to send special LEDs result to addr 13. Retrying.")
                        app_state.current_cpu_task_id = None

                elif task_status == TaskStatus.FAILED:
                    error_msg = task_manager.get_task_error(app_state.current_cpu_task_id)
                    logging.error(f"CPU communication task failed: {error_msg}")
                    app_state.current_cpu_task_id = None
            else:
                logging.warning("No special LEDs result found, skipping sending to address 13.")
                app_state.led_analysis_state = 'CLEARING'

        # --- CLEARING State: 清空日志文件并重置状态 ---
        elif app_state.led_analysis_state == 'CLEARING':
            if app_state.log_file_handler and app_state.log_file_handler in logger.handlers:
                logger.removeHandler(app_state.log_file_handler)
                logging.info("FileHandler removed before clearing.")
            try:
                with open(LOG_FILE, 'w', encoding='utf-8') as f:
                    pass
                logging.info("Log file cleared successfully.")
            except OSError as e:
                logging.error(f"Error clearing log file: {e}")

            app_state.logging_start_time = None
            app_state.last_analysis_result = None
            app_state.last_special_leds_result = None
            app_state.current_analysis_task_id = None
            app_state.current_cpu_task_id = None
            app_state.async_task_progress = ""
            app_state.led_analysis_state = 'IDLE'
            logging.info("'88' analysis cycle complete. Returning to IDLE state.")

        # else: unknown stays unchanged

