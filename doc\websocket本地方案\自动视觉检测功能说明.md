# 自动视觉检测功能说明

## 📋 功能概述

在CPU控制器V页面新增了"启用视觉检测"开关，当启用该开关时，系统会在本地视觉检测程序完成LED状态数据采集后，自动触发网页端的视觉检测流程，实现完全自动化的检测过程。

## 🎯 功能特点

### 1. 智能开关控制
- **位置**：位于"连接设备"按钮前面
- **样式**：参考耦合器页面"自动提交"开关设计
- **状态**：设备未连接时自动禁用，连接后可用
- **持久化**：开关状态自动保存，页面刷新后保持

### 2. 自动触发逻辑
```
数码管显示"88" → 50秒LED数据采集 → 检测完成 → 自动触发视觉检测
```

### 3. 安全检查机制
自动触发前会检查以下条件：
- ✅ 启用视觉检测开关已开启
- ✅ 设备已连接
- ✅ 支持视觉检测功能
- ✅ 当前没有测试在运行
- ✅ 收到正确的完成消息

## 🔧 实现细节

### 1. 新增响应式数据
```javascript
// 自动视觉检测开关状态
const autoVisualDetectionEnabled = ref(false);
```

### 2. WebSocket状态监听
```javascript
// 在状态更新回调中添加自动触发逻辑
if (autoVisualDetectionEnabled.value && 
    state.message && 
    state.message.includes('LED状态数据采集完成')) {
    
    // 延迟1秒后自动触发视觉检测
    setTimeout(() => {
        if (满足所有条件) {
            runVisualInspection();
        }
    }, 1000);
}
```

### 3. 状态持久化
```javascript
// 监听开关状态变化
watch(autoVisualDetectionEnabled, (newValue) => {
    localStorage.setItem('autoVisualDetectionEnabled', newValue.toString());
});

// 页面加载时恢复状态
const saved = localStorage.getItem('autoVisualDetectionEnabled');
if (saved !== null) {
    autoVisualDetectionEnabled.value = saved === 'true';
}
```

### 4. UI组件设计
```html
<!-- 启用视觉检测开关 -->
<div class="flex items-center gap-2 px-3 py-1 rounded-lg bg-opacity-20 backdrop-blur-sm">
    <span class="text-sm font-medium">启用视觉检测</span>
    <el-switch
        v-model="autoVisualDetectionEnabled"
        :disabled="!deviceConnected"
        size="small"
    />
</div>
```

## 🔄 完整工作流程

### 1. 用户操作
```
1. 打开CPU控制器V页面
2. 连接设备
3. 启用"启用视觉检测"开关
4. 等待自动检测流程
```

### 2. 自动检测流程
```
1. 数码管显示"88"
   ↓
2. 本地程序开始50秒LED数据采集
   ↓
3. 网页端显示倒计时和状态
   ↓
4. 采集完成，发送"LED状态数据采集完成"消息
   ↓
5. 网页端检测到消息，自动触发视觉检测
   ↓
6. 执行CPU控制器的视觉检测流程
   ↓
7. 完成整个自动化检测过程
```

### 3. 日志记录
```
[INFO] AUTO_VISUAL: 自动视觉检测功能已启用
[INFO] AUTO_VISUAL: 检测到LED状态数据采集完成，准备自动启动视觉检测流程
[INFO] AUTO_VISUAL: 自动启动视觉检测流程
```

## 🎨 用户界面

### 开关状态显示
- **启用状态**：蓝色开关，显示"启用视觉检测"
- **禁用状态**：灰色开关，显示"启用视觉检测"
- **设备未连接**：开关禁用（灰色不可点击）

### 开关样式特点
- **毛玻璃效果**：`backdrop-blur-sm`
- **半透明背景**：`bg-opacity-20`
- **圆角设计**：`rounded-lg`
- **主题适配**：深色/浅色主题自动切换颜色

## 🛡️ 安全机制

### 1. 条件检查
```javascript
if (supportsVisualInspection.value && 
    !testRunning.value && 
    deviceConnected.value &&
    autoVisualDetectionEnabled.value) {
    // 执行自动触发
} else {
    // 记录跳过原因
}
```

### 2. 防重复触发
- 检查测试运行状态
- 检查设备连接状态
- 检查开关启用状态
- 检查功能支持状态

### 3. 错误处理
- 自动触发失败时记录日志
- 不影响手动操作功能
- 用户可随时关闭自动功能

## 📊 修改文件清单

### 修改的文件
- `static/page_js_css/CPUControllerVue.js`

### 新增内容
1. **响应式数据**：`autoVisualDetectionEnabled`
2. **状态监听**：WebSocket回调中的自动触发逻辑
3. **持久化逻辑**：localStorage保存和恢复
4. **UI组件**：开关组件模板
5. **导出数据**：在return中添加新的响应式数据

### 修改量统计
- **新增代码行数**：约40行
- **修改现有代码**：约10行
- **影响范围**：仅限CPU控制器Vue组件

## 🧪 测试验证

### 1. 功能测试
```bash
# 运行自动视觉检测测试
python test_auto_visual_detection.py
```

### 2. 测试步骤
1. 启动WebSocket服务器
2. 打开网页端，连接设备
3. 启用"启用视觉检测"开关
4. 模拟数码管"88"检测
5. 观察自动触发效果

### 3. 验证要点
- ✅ 开关状态正确显示
- ✅ 状态持久化正常
- ✅ 自动触发条件检查
- ✅ 日志记录完整
- ✅ 不影响手动操作

## 🚀 使用指南

### 1. 启用自动检测
1. 确保设备已连接
2. 点击"启用视觉检测"开关
3. 开关变为蓝色表示已启用

### 2. 观察自动流程
1. 等待数码管显示"88"
2. 观察50秒倒计时
3. 检测完成后自动触发视觉检测
4. 查看日志了解详细过程

### 3. 关闭自动功能
1. 点击开关关闭自动功能
2. 恢复手动操作模式

## 🔮 扩展可能

### 1. 功能增强
- 支持自定义触发条件
- 添加自动检测次数限制
- 支持检测间隔时间设置

### 2. 用户体验
- 添加声音提示
- 显示自动检测统计
- 支持快捷键控制

### 3. 高级功能
- 支持批量自动检测
- 添加检测结果自动分析
- 实现检测报告自动生成

---

**实施完成时间**：2025年8月21日  
**版本**：v1.0  
**状态**：✅ 已完成并集成到系统中
