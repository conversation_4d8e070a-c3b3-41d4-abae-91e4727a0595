"""
Enable high-resolution (1ms) system timer on Windows to stabilize scheduling
and make processing frequency independent of window visibility.
Automatically reverts on process exit.
"""
import platform
import ctypes
import atexit

def enable_1ms():
    if platform.system() == "Windows":
        try:
            ctypes.windll.winmm.timeBeginPeriod(1)
            atexit.register(lambda: ctypes.windll.winmm.timeEndPeriod(1))
        except Exception:
            # Fail silently; not critical on non-Windows or restricted envs
            pass

