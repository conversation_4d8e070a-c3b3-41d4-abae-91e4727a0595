#!/usr/bin/env python3
"""
测试主程序的WebSocket集成
跳过摄像头初始化，只测试WebSocket功能
"""

import logging
import time
from simple_websocket_server import simple_websocket_server

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_main_websocket():
    """测试主程序的WebSocket集成"""
    print("=== 测试主程序WebSocket集成 ===")
    
    try:
        # 1. 启动WebSocket服务器
        print("1. 启动WebSocket服务器...")
        simple_websocket_server.start_server()
        time.sleep(2)
        
        if simple_websocket_server.running:
            print("✓ WebSocket服务器启动成功")
        else:
            print("✗ WebSocket服务器启动失败")
            return False
        
        # 2. 模拟数码管检测到"88"
        print("\n2. 模拟数码管检测到'88'...")
        
        # 导入数码管检测模块的函数
        from digit_detector import _check_for_88_trigger
        
        # 模拟检测到"88"
        recognized_chars = ['8', '8']
        _check_for_88_trigger(recognized_chars)
        print("✓ 已触发88检测逻辑")
        
        # 3. 等待倒计时完成
        print("\n3. 等待倒计时完成...")
        print("   可以在浏览器中打开网页测试连接")
        print("   WebSocket地址: ws://localhost:8766")
        
        # 等待60秒让倒计时完成
        for i in range(60, 0, -1):
            print(f"\r   等待时间: {i}秒", end="", flush=True)
            time.sleep(1)
            
            # 检查是否还在活动状态
            if not simple_websocket_server.is_active:
                print(f"\n✓ 检测已完成，用时: {60-i}秒")
                break
        
        print("\n\n4. 停止WebSocket服务器...")
        simple_websocket_server.stop_server()
        print("✓ WebSocket服务器已停止")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        logging.error(f"测试错误: {e}")
        return False
    finally:
        # 确保服务器被停止
        try:
            simple_websocket_server.stop_server()
        except:
            pass

if __name__ == "__main__":
    success = test_main_websocket()
    if success:
        print("\n=== 测试完成：WebSocket集成工作正常 ===")
    else:
        print("\n=== 测试失败：WebSocket集成存在问题 ===")
