"""
CaptureWorker: dedicated capture thread that reads frames from app_state.cap
and pushes the latest frame into app_state.frame_queue (maxsize=1).
"""
import threading
import queue
import time
import logging
import cv2

class CaptureWorker:
    def __init__(self, app_state):
        self.app_state = app_state
        self.thread = None
        self.running = False

    def start(self):
        if self.thread and self.thread.is_alive():
            return
        if self.app_state.cap is None or not self.app_state.cap.isOpened():
            logging.error("Cannot start CaptureWorker: camera not initialized")
            return
        self.running = True
        self.thread = threading.Thread(target=self._loop, daemon=True)
        self.thread.start()
        logging.info("采集线程已启动")

    def stop(self):
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        logging.info("采集线程已停止")

    def _loop(self):
        cap = self.app_state.cap
        fq = self.app_state.frame_queue
        while self.running and cap and cap.isOpened():
            try:
                ret, frame = cap.read()
                if not ret or frame is None:
                    time.sleep(0.005)
                    continue
                # 写入最新帧
                try:
                    fq.put_nowait(frame)
                except queue.Full:
                    try:
                        fq.get_nowait()
                    except Exception:
                        pass
                    try:
                        fq.put_nowait(frame)
                    except Exception:
                        pass
                # 可适当让出
                # time.sleep(0)
            except Exception as e:
                logging.error(f"Capture thread error: {e}")
                time.sleep(0.02)

