import cv2
import time

# 1. 尝试不同的后端 (backend)
# 在Windows上，你可以尝试使用DirectShow后端
cap = cv2.VideoCapture(1, cv2.CAP_DSHOW)

# 检查摄像头是否成功打开
if not cap.isOpened():
    print("无法打开摄像头")
    exit()

# 初始设置曝光和亮度参数
exposure_value = -7  # 初始曝光值
brightness_value = -10  # 初始亮度值

# 设置分辨率 - 添加了更多分辨率包括2K
resolution_presets = [
    (640, 480),      # VGA
    (800, 600),      # SVGA
    (1024, 768),     # XGA
    (1280, 720),     # HD 720p
    (1280, 800),     # WXGA
    (1280, 960),     # SXGA-
    (1280, 1024),    # SXGA
    (1440, 900),     # WXGA+
    (1600, 900),     # HD+
    (1600, 1200),    # UXGA
    (1680, 1050),    # WSXGA+
    (1920, 1080),    # Full HD 1080p
    (1920, 1200),    # WUXGA
    (2048, 1080),    # 2K DCI
    (2048, 1536),    # QXGA
    (2560, 1440),    # QHD/WQHD (2K)
    (2560, 1600),    # WQXGA
    (320, 240),      # QVGA (最小，放在最后)
]
current_resolution_index = 0
width, height = resolution_presets[current_resolution_index]
cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)

# 调整曝光 - 负值会降低曝光度（减小这个值可以减少曝光）
cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 关闭自动曝光
cap.set(cv2.CAP_PROP_EXPOSURE, exposure_value)  # 初始曝光值

# 调整亮度 - 默认是0，减小这个值会降低亮度
cap.set(cv2.CAP_PROP_BRIGHTNESS, brightness_value)  # 初始亮度值

# 2. 增加初始化延迟
time.sleep(1)  # 给摄像头一秒钟的初始化时间

# 获取实际分辨率（可能与请求的不同）
actual_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
actual_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
print(f"当前分辨率: {int(actual_width)}x{int(actual_height)}")

print("按键控制:")
print("e/d: 增加/减少曝光值")
print("r/f: 增加/减少亮度值")
print("t: 切换分辨率")
print("s: 保存当前图像")
print("q: 退出程序")

# 添加帧率计算
fps_start_time = time.time()
fps_frame_count = 0
fps = 0

# 添加分辨率信息显示列表
supported_resolutions = []

while True:
    # 逐帧捕获图像
    ret, frame = cap.read()
    
    # 如果正确读取帧，ret为True
    if not ret:
        print("无法接收帧（视频流结束？）")
        break
    
    # 计算帧率
    fps_frame_count += 1
    if (time.time() - fps_start_time) > 1.0:
        fps = fps_frame_count / (time.time() - fps_start_time)
        fps_start_time = time.time()
        fps_frame_count = 0
    
    # 获取当前帧的尺寸
    frame_height, frame_width = frame.shape[:2]
    
    # 在图像上显示当前的参数值
    info_text = f"曝光: {exposure_value}, 亮度: {brightness_value}"
    resolution_text = f"分辨率: {frame_width}x{frame_height}"
    fps_text = f"FPS: {fps:.1f}"
    
    cv2.putText(frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    cv2.putText(frame, resolution_text, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    cv2.putText(frame, fps_text, (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    # 显示结果帧
    cv2.imshow('摄像头', frame)
    
    # 等待按键
    key = cv2.waitKey(1)
    
    # 按'q'键退出循环
    if key == ord('q'):
        break
    # 增加曝光值 (e键)
    elif key == ord('e'):
        exposure_value += 1
        cap.set(cv2.CAP_PROP_EXPOSURE, exposure_value)
        print(f"曝光值增加到: {exposure_value}")
    # 减少曝光值 (d键)
    elif key == ord('d'):
        exposure_value -= 1
        cap.set(cv2.CAP_PROP_EXPOSURE, exposure_value)
        print(f"曝光值减少到: {exposure_value}")
    # 增加亮度 (r键)
    elif key == ord('r'):
        brightness_value += 1
        cap.set(cv2.CAP_PROP_BRIGHTNESS, brightness_value)
        print(f"亮度值增加到: {brightness_value}")
    # 减少亮度 (f键)
    elif key == ord('f'):
        brightness_value -= 1
        cap.set(cv2.CAP_PROP_BRIGHTNESS, brightness_value)
        print(f"亮度值减少到: {brightness_value}")
    # 切换分辨率 (t键)
    elif key == ord('t'):
        current_resolution_index = (current_resolution_index + 1) % len(resolution_presets)
        width, height = resolution_presets[current_resolution_index]
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
        # 获取实际设置的分辨率
        actual_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
        actual_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
        
        # 记录是否成功设置此分辨率
        req_res = f"请求: {width}x{height}"
        actual_res = f"实际: {int(actual_width)}x{int(actual_height)}"
        resolution_result = f"{req_res}, {actual_res}"
        if resolution_result not in [item["info"] for item in supported_resolutions]:
            is_supported = (int(actual_width) == width and int(actual_height) == height)
            supported_resolutions.append({
                "info": resolution_result,
                "supported": is_supported
            })
        
        print(f"分辨率切换至: {int(actual_width)}x{int(actual_height)}")
    # 保存当前图像 (s键)
    elif key == ord('s'):
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"capture_{frame_width}x{frame_height}_{timestamp}.jpg"
        cv2.imwrite(filename, frame)
        print(f"图像已保存为: {filename}")

# 完成所有操作后，释放VideoCapture对象
cap.release()
cv2.destroyAllWindows()

# 打印最终使用的设置值和支持的分辨率列表
print("\n最终设置:")
print(f"曝光值: {exposure_value}")
print(f"亮度值: {brightness_value}")
print(f"分辨率: {int(actual_width)}x{int(actual_height)}")

print("\n测试的分辨率列表:")
for res in supported_resolutions:
    support_status = "✓ 支持" if res["supported"] else "✗ 不支持"
    print(f"{res['info']} - {support_status}")

print("\n注意: 某些摄像头可能会自动调整到最接近的支持分辨率")
