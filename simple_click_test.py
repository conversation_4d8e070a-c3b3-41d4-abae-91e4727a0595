"""
简化版调试窗口 - 专门测试点击计数
"""
import tkinter as tk
import time
import threading

class SimpleClickWindow:
    """简化版点击测试窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("点击计数测试")
        self.root.geometry("200x100+300+300")
        
        # 计数器
        self.click_count = 0
        
        # 创建标签显示计数
        self.label = tk.Label(
            self.root,
            text=f"点击次数: {self.click_count}",
            font=("Arial", 16),
            bg="lightblue"
        )
        self.label.pack(expand=True, fill=tk.BOTH)
        
        # 绑定点击事件
        self.label.bind("<Button-1>", self.on_click)
        
        # 添加说明
        info_label = tk.Label(
            self.root,
            text="点击窗口任意位置增加计数",
            font=("Arial", 10)
        )
        info_label.pack(side=tk.BOTTOM)
        
        # 置顶
        self.root.attributes('-topmost', True)
        
    def on_click(self, event):
        """处理点击事件"""
        self.click_count += 1
        self.label.config(text=f"点击次数: {self.click_count}")
        
        # 改变颜色
        colors = ["lightblue", "lightgreen", "lightyellow", "lightcoral", "lightpink"]
        color = colors[self.click_count % len(colors)]
        self.label.config(bg=color)
        
        print(f"🖱️  点击了第 {self.click_count} 次")
    
    def run(self):
        """运行窗口"""
        print("🚀 简化版点击测试窗口启动")
        print("点击窗口测试计数功能")
        self.root.mainloop()

if __name__ == "__main__":
    window = SimpleClickWindow()
    window.run()