"""
调试版幽灵窗口 - 更容易看见和测试
"""
import tkinter as tk
import time
import logging
import threading
import sys

class DebugGhostWindow:
    """调试版幽灵窗口 - 更大更醒目"""

    def __init__(self, window_title="DebugGhostWindow"):
        self.window_title = window_title
        self.is_running = False
        self.thread = None
        self.root = None
        self.window = None
        
        # 调试设置 - 更容易看见
        self.window_size = 50  # 50x50像素（原来2x2）
        self.window_x = 100    # 位置更明显
        self.window_y = 100
        self.window_color = "#FF0000"  # 红色（原来灰色）
        
        # 调试信息
        self.update_count = 0
        self.start_time = None
        
    def create_window(self):
        """创建幽灵窗口"""
        try:
            # 创建Tk根窗口
            self.root = tk.Tk()
            self.root.withdraw()  # 隐藏主窗口
            
            # 创建幽灵窗口
            self.window = tk.Toplevel(self.root)
            self.window.title(self.window_title)
            self.window.geometry(f"{self.window_size}x{self.window_size}+{self.window_x}+{self.window_y}")
            
            # 设置窗口属性
            self.window.overrideredirect(True)  # 无边框
            self.window.attributes('-topmost', True)  # 置顶
            self.window.attributes('-alpha', 0.9)  # 更不透明
            
            # 创建标签作为背景
            label = tk.Label(
                self.window, 
                bg=self.window_color, 
                width=self.window_size, 
                height=self.window_size,
                fg="white",  # 白色文字
                font=("Arial", 8, "bold")  # 字体
            )
            label.pack()
            
            # 添加调试文字
            label.config(text="G")  # 显示"G"表示Ghost
            
            # 绑定关闭事件
            self.window.protocol("WM_DELETE_WINDOW", self.on_window_close)
            
            # 记录启动时间
            self.start_time = time.time()
            
            print(f"✅ 调试幽灵窗口已创建！")
            print(f"   位置: ({self.window_x}, {self.window_y})")
            print(f"   大小: {self.window_size}x{self.window_size}")
            print(f"   颜色: {self.window_color}")
            print(f"   请在屏幕左上角查找红色方块")
            
            logging.info(f"调试幽灵窗口 '{self.window_title}' 创建成功")
            
        except Exception as e:
            logging.error(f"创建调试幽灵窗口失败: {e}")
            self.window = None
    
    def update_window(self):
        """更新窗口"""
        if self.window is None or self.root is None:
            return
            
        try:
            self.update_count += 1
            
            # 每5次更新改变一次颜色（便于观察）
            colors = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF"]
            color_index = (self.update_count // 5) % len(colors)
            self.window_color = colors[color_index]
            
            # 更新窗口位置（小范围移动）
            offset_x = (self.update_count // 10) % 20
            offset_y = (self.update_count // 15) % 20
            current_x = self.window_x + offset_x
            current_y = self.window_y + offset_y
            
            # 更新窗口位置
            self.window.geometry(f"{self.window_size}x{self.window_size}+{current_x}+{current_y}")
            
            # 更新窗口颜色
            for widget in self.window.winfo_children():
                if isinstance(widget, tk.Label):
                    widget.config(bg=self.window_color)
                    # 更新显示文字
                    widget.config(text=str(self.update_count % 10))
            
            # 处理Tkinter事件
            self.root.update()
            
            # 每10次更新打印一次状态
            if self.update_count % 10 == 0:
                elapsed = time.time() - self.start_time
                print(f"🔄 幽灵窗口更新 #{self.update_count}, 运行时间: {elapsed:.1f}s")
            
        except Exception as e:
            logging.error(f"更新调试幽灵窗口失败: {e}")
    
    def on_window_close(self):
        """窗口关闭事件"""
        print("⚠️  调试幽灵窗口被用户关闭")
        logging.info("调试幽灵窗口被用户关闭")
        self.window.destroy()
        self.window = None
    
    def _run_loop(self):
        """幽灵窗口运行循环"""
        print("🚀 调试幽灵窗口线程启动")
        logging.info("调试幽灵窗口线程启动")
        
        # 等待延迟时间后再创建窗口
        print("⏳ 等待2秒后创建调试幽灵窗口...")
        time.sleep(2.0)
        
        # 创建窗口
        self.create_window()
        
        if self.window is None:
            print("❌ 无法创建调试幽灵窗口")
            logging.error("无法创建调试幽灵窗口")
            return
        
        # 运行窗口更新循环
        try:
            while self.is_running and self.window is not None:
                self.update_window()
                time.sleep(1.0)  # 每秒更新一次
                
        except Exception as e:
            logging.error(f"调试幽灵窗口运行错误: {e}")
        
        # 清理
        if self.window:
            try:
                self.window.destroy()
            except:
                pass
        
        if self.root:
            try:
                self.root.destroy()
            except:
                pass
        
        print("🛑 调试幽灵窗口线程停止")
        logging.info("调试幽灵窗口线程停止")
    
    def start(self):
        """启动幽灵窗口"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # 启动线程
        self.thread = threading.Thread(target=self._run_loop, daemon=True)
        self.thread.start()
        
        print("✅ 调试幽灵窗口已启动")
        logging.info("调试幽灵窗口已启动")
    
    def stop(self):
        """停止幽灵窗口"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 关闭窗口
        if self.window:
            try:
                self.window.destroy()
            except Exception as e:
                logging.error(f"关闭调试幽灵窗口失败: {e}")
        
        if self.root:
            try:
                self.root.destroy()
            except Exception as e:
                logging.error(f"关闭Tkinter根窗口失败: {e}")
        
        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)
        
        self.window = None
        self.root = None
        print("🛑 调试幽灵窗口已关闭")
        logging.info("调试幽灵窗口已关闭")
    
    def set_position(self, x, y):
        """设置窗口位置"""
        self.window_x = x
        self.window_y = y
        if self.window:
            try:
                self.window.geometry(f"{self.window_size}x{self.window_size}+{x}+{y}")
                print(f"📍 调试幽灵窗口位置已移动到: ({x}, {y})")
            except Exception as e:
                logging.error(f"移动调试幽灵窗口失败: {e}")
    
    def set_color(self, rgb_color):
        """设置窗口颜色"""
        # 将RGB转换为十六进制颜色
        r, g, b = rgb_color
        self.window_color = f"#{r:02x}{g:02x}{b:02x}"
        if self.window:
            try:
                for widget in self.window.winfo_children():
                    if isinstance(widget, tk.Label):
                        widget.config(bg=self.window_color)
                print(f"🎨 调试幽灵窗口颜色已变更为: {self.window_color}")
            except Exception as e:
                logging.error(f"设置调试幽灵窗口颜色失败: {e}")

# 全局调试幽灵窗口实例
debug_ghost_window = None

def initialize_debug_ghost_window():
    """初始化全局调试幽灵窗口"""
    global debug_ghost_window
    if debug_ghost_window is None:
        debug_ghost_window = DebugGhostWindow()
        debug_ghost_window.start()
        logging.info("全局调试幽灵窗口已初始化")
    return debug_ghost_window

def shutdown_debug_ghost_window():
    """关闭全局调试幽灵窗口"""
    global debug_ghost_window
    if debug_ghost_window is not None:
        debug_ghost_window.stop()
        debug_ghost_window = None
        logging.info("全局调试幽灵窗口已关闭")

def get_debug_ghost_window():
    """获取全局调试幽灵窗口实例"""
    global debug_ghost_window
    return debug_ghost_window