"""
简化的WebSocket服务器模块
用于向网页端实时推送视觉检测状态和倒计时信息
支持LED日志监控和推送功能
"""

import asyncio
import websockets
import json
import logging
import threading
import os
import re
from typing import Set, Dict, Any, List
from datetime import datetime
from enum import Enum
from collections import deque


class DetectionState(Enum):
    """检测状态枚举"""
    IDLE = "IDLE"
    LOGGING = "LOGGING"
    ANALYZING = "ANALYZING"
    COMPLETED = "COMPLETED"


class LEDLogMonitor:
    """LED日志文件监控器 - 智能过滤和推送关键事件"""

    def __init__(self, log_file_path: str, websocket_server):
        self.log_file_path = log_file_path
        self.websocket_server = websocket_server
        self.is_monitoring = False
        self.monitor_thread = None
        self.last_position = 0
        self.led_states = {}  # 缓存LED状态用于检测变化
        self.last_summary_time = 0
        self.summary_interval = 5  # 每5秒发送一次状态摘要

        # 日志解析正则表达式
        self.led_pattern = re.compile(r'LED (G\d+|R\d+): Status=(\w+), Gray=([\d.]+), Green=([\d.]+), Red=([\d.]+), Brightness=([\d.]+)%')
        self.state_change_pattern = re.compile(r'LED (G\d+|R\d+) state (\w+)->(\w+)')
        self.warning_pattern = re.compile(r'WARNING - (.+)')

    def start_monitoring(self):
        """开始监控LED日志文件"""
        if self.is_monitoring:
            return

        self.is_monitoring = True
        self.last_position = self._get_file_size()  # 从文件末尾开始监控

        # 先读取现有的LED状态
        self._read_existing_led_states()

        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logging.info(f"LED日志监控已启动: {self.log_file_path}")

        # 发送初始的详细状态
        if self.led_states:
            self._send_detailed_led_status()

    def stop_monitoring(self):
        """停止监控LED日志文件"""
        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2)
        logging.info("LED日志监控已停止")

    def _get_file_size(self):
        """获取文件大小"""
        try:
            if os.path.exists(self.log_file_path):
                return os.path.getsize(self.log_file_path)
        except Exception:
            pass
        return 0

    def _read_existing_led_states(self):
        """读取现有的LED状态（从日志文件末尾向前读取最近的状态）"""
        try:
            if not os.path.exists(self.log_file_path):
                return

            # 读取文件最后几KB的内容来获取最新的LED状态
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                f.seek(0, 2)  # 移动到文件末尾
                file_size = f.tell()

                # 读取最后10KB的内容（足够获取所有LED的最新状态）
                read_size = min(10240, file_size)
                f.seek(max(0, file_size - read_size))
                content = f.read()

                # 解析LED状态
                lines = content.split('\n')
                for line in reversed(lines):  # 从最新的行开始解析
                    if not line.strip():
                        continue

                    # 解析LED状态数据
                    led_match = self.led_pattern.search(line)
                    if led_match:
                        led_id = led_match.group(1)
                        status = led_match.group(2)
                        brightness = float(led_match.group(6))

                        # 只有当LED状态还未记录时才添加（保证是最新的状态）
                        if led_id not in self.led_states:
                            self.led_states[led_id] = {
                                'status': status,
                                'brightness': brightness,
                                'timestamp': self._extract_timestamp(line)
                            }

                # 确保所有LED都有初始状态
                self._ensure_all_leds_initialized()

        except Exception as e:
            logging.error(f"读取现有LED状态失败: {e}")

    def _ensure_all_leds_initialized(self):
        """确保所有LED都有初始状态"""
        # 初始化G1-G33
        for i in range(1, 34):
            led_id = f"G{i}"
            if led_id not in self.led_states:
                self.led_states[led_id] = {
                    'status': 'OFF',
                    'brightness': 0.0,
                    'timestamp': datetime.now().strftime("%H:%M:%S.%f")[:-3]
                }

        # 初始化R1-R2
        for i in range(1, 3):
            led_id = f"R{i}"
            if led_id not in self.led_states:
                self.led_states[led_id] = {
                    'status': 'OFF',
                    'brightness': 0.0,
                    'timestamp': datetime.now().strftime("%H:%M:%S.%f")[:-3]
                }

    def _monitor_loop(self):
        """监控循环 - 检查文件变化并处理新日志"""
        while self.is_monitoring:
            try:
                current_size = self._get_file_size()
                if current_size > self.last_position:
                    # 文件有新内容
                    new_lines = self._read_new_lines(current_size)
                    if new_lines:
                        self._process_new_lines(new_lines)
                    self.last_position = current_size

                # 检查是否需要发送状态摘要
                current_time = datetime.now().timestamp()
                if current_time - self.last_summary_time >= self.summary_interval:
                    self._send_status_summary()
                    self.last_summary_time = current_time

            except Exception as e:
                logging.error(f"LED日志监控错误: {e}")

            threading.Event().wait(0.05)  # 每100ms检查一次（降低轮询延迟）

    def _read_new_lines(self, current_size: int) -> List[str]:
        """读取文件中的新行"""
        try:
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                f.seek(self.last_position)
                new_content = f.read(current_size - self.last_position)
                return new_content.strip().split('\n') if new_content.strip() else []
        except Exception as e:
            logging.error(f"读取LED日志文件失败: {e}")
            return []

    def _process_new_lines(self, lines: List[str]):
        """处理新的日志行 - 智能过滤关键事件"""
        for line in lines:
            if not line.strip():
                continue

            # 检查LED状态变化
            state_change_match = self.state_change_pattern.search(line)
            if state_change_match:
                led_id = state_change_match.group(1)
                from_state = state_change_match.group(2)
                to_state = state_change_match.group(3)
                timestamp = self._extract_timestamp(line)

                self._send_led_state_change(led_id, from_state, to_state, timestamp)
                continue

            # 检查性能警告
            warning_match = self.warning_pattern.search(line)
            if warning_match:
                warning_msg = warning_match.group(1)
                timestamp = self._extract_timestamp(line)
                self._send_performance_warning(warning_msg, timestamp)
                continue

            # 解析LED状态数据（用于状态摘要）
            led_match = self.led_pattern.search(line)
            if led_match:
                led_id = led_match.group(1)
                status = led_match.group(2)
                brightness = float(led_match.group(6))

                # 更新LED状态缓存
                self.led_states[led_id] = {
                    'status': status,
                    'brightness': brightness,
                    'timestamp': self._extract_timestamp(line)
                }

    def _extract_timestamp(self, line: str) -> str:
        """从日志行中提取时间戳"""
        try:
            # 提取格式如 "2025-08-22 09:46:30.366" 的时间戳
            timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})', line)
            if timestamp_match:
                return timestamp_match.group(1)
        except Exception:
            pass
        return datetime.now().strftime("%H:%M:%S.%f")[:-3]

    def _send_led_state_change(self, led_id: str, from_state: str, to_state: str, timestamp: str):
        """发送LED状态变化事件"""
        message = {
            "type": "led_state_change",
            "data": {
                "led": led_id,
                "from": from_state,
                "to": to_state,
                "timestamp": timestamp
            }
        }
        self.websocket_server._broadcast_led_message_sync(message)

        # 同时发送详细状态更新（用于弹窗显示）
        self._send_detailed_led_status()

    def _send_performance_warning(self, warning_msg: str, timestamp: str):
        """发送性能警告"""
        message = {
            "type": "led_performance_warning",
            "data": {
                "message": warning_msg,
                "timestamp": timestamp
            }
        }
        self.websocket_server._broadcast_led_message_sync(message)

    def _send_status_summary(self):
        """发送LED状态摘要"""
        if not self.led_states:
            return

        green_leds = {k: v for k, v in self.led_states.items() if k.startswith('G')}
        red_leds = {k: v for k, v in self.led_states.items() if k.startswith('R')}

        green_on = sum(1 for led in green_leds.values() if led['status'] == 'ON')
        red_on = sum(1 for led in red_leds.values() if led['status'] == 'ON')

        green_avg_brightness = sum(led['brightness'] for led in green_leds.values()) / len(green_leds) if green_leds else 0
        red_avg_brightness = sum(led['brightness'] for led in red_leds.values()) / len(red_leds) if red_leds else 0

        message = {
            "type": "led_status_summary",
            "data": {
                "timestamp": datetime.now().strftime("%H:%M:%S"),
                "green_leds": {
                    "total": len(green_leds),
                    "on": green_on,
                    "off": len(green_leds) - green_on,
                    "avg_brightness": f"{green_avg_brightness:.1f}%"
                },
                "red_leds": {
                    "total": len(red_leds),
                    "on": red_on,
                    "off": len(red_leds) - red_on,
                    "avg_brightness": f"{red_avg_brightness:.1f}%"
                }
            }
        }
        self.websocket_server._broadcast_led_message_sync(message)

    def _send_detailed_led_status(self):
        """发送详细的LED状态（用于弹窗显示）"""
        if not self.led_states:
            return

        # 构建详细状态消息
        message = {
            "type": "led_detailed_status",
            "data": {
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3],
                "all_leds": {}
            }
        }

        # 添加所有LED状态
        for led_id, led_data in self.led_states.items():
            message["data"]["all_leds"][led_id] = {
                "status": led_data["status"],
                "brightness": led_data["brightness"],
                "lastChanged": led_data["timestamp"]
            }

        self.websocket_server._broadcast_led_message_sync(message)


class SimpleWebSocketServer:
    """简化的WebSocket服务器类"""

    def __init__(self, host: str = "localhost", port: int = 8766):
        self.host = host
        self.port = port
        self.clients: Set = set()
        self.server = None
        self.loop = None
        self.thread = None
        self.running = False

        # 检测状态管理
        self.current_state = DetectionState.IDLE
        self.countdown_total = 0
        self.countdown_remaining = 0
        self.start_time = None
        self.message = "等待检测..."
        self.is_active = False

        # LED日志监控器
        self.led_monitor = None
        self.led_log_path = None

        # 线程安全锁
        self._state_lock = threading.Lock()
        
    def start_server(self):
        """启动WebSocket服务器"""
        if self.running:
            logging.warning("WebSocket服务器已经在运行")
            return
            
        self.thread = threading.Thread(target=self._run_server_thread, daemon=True)
        self.thread.start()
        logging.info(f"WebSocket服务器启动线程已创建，地址: ws://{self.host}:{self.port}")
        
    def stop_server(self):
        """停止WebSocket服务器"""
        if not self.running:
            return

        self.running = False

        # 停止LED日志监控
        if self.led_monitor:
            self.led_monitor.stop_monitoring()

        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)

        logging.info("WebSocket服务器已停止")
        
    def _run_server_thread(self):
        """在独立线程中运行WebSocket服务器"""
        try:
            # 创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.running = True
            
            # 启动服务器
            self.loop.run_until_complete(self._run_server())
            
        except Exception as e:
            logging.error(f"WebSocket服务器运行错误: {e}")
        finally:
            self.running = False
            
    async def _run_server(self):
        """异步运行WebSocket服务器"""
        try:
            # 启动WebSocket服务器
            async with websockets.serve(self._handle_client, self.host, self.port):
                logging.info(f"WebSocket服务器已启动: ws://{self.host}:{self.port}")
                
                # 保持服务器运行
                while self.running:
                    await asyncio.sleep(1)
                    
        except Exception as e:
            logging.error(f"WebSocket服务器异步运行错误: {e}")
            
    async def _handle_client(self, websocket):
        """处理客户端连接"""
        client_addr = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logging.info(f"WebSocket客户端连接: {client_addr}")
        
        # 添加到客户端集合
        self.clients.add(websocket)
        
        try:
            # 发送当前状态给新连接的客户端
            await self._send_current_status(websocket)
            
            # 处理客户端消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self._handle_message(websocket, data)
                except json.JSONDecodeError:
                    logging.warning(f"收到无效JSON消息: {message}")
                except Exception as e:
                    logging.error(f"处理客户端消息错误: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logging.info(f"WebSocket客户端断开连接: {client_addr}")
        except Exception as e:
            logging.error(f"WebSocket连接处理错误: {e}")
        finally:
            # 从客户端集合中移除
            self.clients.discard(websocket)
            
    async def _handle_message(self, websocket, data: Dict[str, Any]):
        """处理客户端消息"""
        message_type = data.get("type", "")
        
        if message_type == "query_status":
            # 客户端查询当前状态
            await self._send_current_status(websocket)
        elif message_type == "ping":
            # 心跳检测
            await websocket.send(json.dumps({
                "type": "pong",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }))
        else:
            logging.warning(f"未知消息类型: {message_type}")
            
    async def _send_current_status(self, websocket):
        """发送当前状态给指定客户端"""
        with self._state_lock:
            status_data = {
                "type": "status_update",
                "data": {
                    "state": self.current_state.value,
                    "countdown": self.countdown_remaining,
                    "total_duration": self.countdown_total,
                    "progress": self._calculate_progress(),
                    "message": self.message,
                    "is_active": self.is_active,
                    "start_time": self.start_time.strftime("%H:%M:%S") if self.start_time else None,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            }
            
        try:
            await websocket.send(json.dumps(status_data))
        except Exception as e:
            logging.error(f"发送状态更新失败: {e}")
            
    def _calculate_progress(self) -> int:
        """计算进度百分比"""
        if self.countdown_total <= 0:
            return 0
        return int((self.countdown_total - self.countdown_remaining) / self.countdown_total * 100)
        
    def _broadcast_status_sync(self):
        """同步广播状态更新（从其他线程调用）"""
        if not self.loop or not self.running:
            return
            
        # 使用线程安全的方式调度协程
        try:
            future = asyncio.run_coroutine_threadsafe(self._broadcast_status(), self.loop)
            # 不等待结果，避免阻塞
        except Exception as e:
            logging.error(f"广播状态更新失败: {e}")
            
    async def _broadcast_status(self):
        """异步广播状态更新"""
        if not self.clients:
            return
            
        with self._state_lock:
            status_data = {
                "type": "status_update",
                "data": {
                    "state": self.current_state.value,
                    "countdown": self.countdown_remaining,
                    "total_duration": self.countdown_total,
                    "progress": self._calculate_progress(),
                    "message": self.message,
                    "is_active": self.is_active,
                    "start_time": self.start_time.strftime("%H:%M:%S") if self.start_time else None,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            }
            
        message = json.dumps(status_data)
        
        # 发送给所有客户端
        disconnected_clients = set()
        for client in self.clients.copy():
            try:
                await client.send(message)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                logging.warning(f"向客户端发送消息失败: {e}")
                disconnected_clients.add(client)
                
        # 移除断开连接的客户端
        self.clients -= disconnected_clients

    async def _broadcast_led_message(self, message_data: Dict[str, Any]):
        """广播LED日志消息给所有客户端"""
        if not self.clients:
            return

        message = json.dumps(message_data)

        # 发送给所有客户端
        disconnected_clients = set()
        for client in self.clients.copy():
            try:
                await client.send(message)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                logging.warning(f"向客户端发送LED消息失败: {e}")
                disconnected_clients.add(client)

        # 移除断开连接的客户端
        self.clients -= disconnected_clients

    def _broadcast_led_message_sync(self, message_data: Dict[str, Any]):
        """线程安全的LED消息广播方法"""
        if not self.loop or not self.running:
            return

        # 使用线程安全的方式调度协程
        try:
            asyncio.run_coroutine_threadsafe(self._broadcast_led_message(message_data), self.loop)
        except Exception as e:
            logging.error(f"广播LED消息失败: {e}")

    def broadcast_digit_display(self, display_value: str):
        """广播数码管显示值"""
        if not self.loop or not self.running:
            return

        message_data = {
            "type": "digit_display_update",
            "data": {
                "display_value": display_value,
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3]
            }
        }

        # 使用线程安全的方式调度协程
        try:
            asyncio.run_coroutine_threadsafe(self._broadcast_led_message(message_data), self.loop)
        except Exception as e:
            logging.error(f"广播数码管显示失败: {e}")

    # 公共接口方法，供主程序调用
    def start_detection(self, duration: int = 50, message: str = "正在记录LED状态数据..."):
        """开始检测，启动倒计时"""
        with self._state_lock:
            self.current_state = DetectionState.LOGGING
            self.countdown_total = duration
            self.countdown_remaining = duration
            self.start_time = datetime.now()
            self.message = message
            self.is_active = True
            
        # 启动LED日志监控（如果已配置）
        if self.led_monitor and not self.led_monitor.is_monitoring:
            self.led_monitor.start_monitoring()

        # 广播状态更新
        self._broadcast_status_sync()
        logging.info(f"开始检测倒计时: {duration}秒")
        
    def update_countdown(self, remaining: int, message: str = None):
        """更新倒计时"""
        with self._state_lock:
            self.countdown_remaining = max(0, remaining)
            if message:
                self.message = message
                
        # 广播状态更新
        self._broadcast_status_sync()
            
    def set_analyzing_state(self, message: str = "正在分析检测结果..."):
        """设置为分析状态"""
        with self._state_lock:
            self.current_state = DetectionState.ANALYZING
            self.countdown_remaining = 0
            self.message = message
            
        # 广播状态更新
        self._broadcast_status_sync()
            
    def complete_detection(self, message: str = "检测完成"):
        """完成检测"""
        with self._state_lock:
            self.current_state = DetectionState.COMPLETED
            self.countdown_remaining = 0
            self.message = message
            self.is_active = False
            
        # 广播状态更新
        self._broadcast_status_sync()
        
        # 3秒后自动回到空闲状态
        threading.Timer(3.0, self.reset_to_idle).start()
            
    def reset_to_idle(self):
        """重置到空闲状态"""
        with self._state_lock:
            self.current_state = DetectionState.IDLE
            self.countdown_total = 0
            self.countdown_remaining = 0
            self.start_time = None
            self.message = "等待检测..."
            self.is_active = False
            
        # 广播状态更新
        self._broadcast_status_sync()

        # 停止LED日志监控
        if self.led_monitor:
            self.led_monitor.stop_monitoring()
            self.led_monitor = None

    def start_led_monitoring(self, log_file_path: str):
        """启动LED日志监控"""
        if self.led_monitor:
            self.led_monitor.stop_monitoring()

        self.led_log_path = log_file_path
        self.led_monitor = LEDLogMonitor(log_file_path, self)

        # 只有在检测活动时才启动监控
        if self.is_active:
            self.led_monitor.start_monitoring()
            logging.info(f"LED日志监控已启动: {log_file_path}")

    def stop_led_monitoring(self):
        """停止LED日志监控"""
        if self.led_monitor:
            self.led_monitor.stop_monitoring()
            self.led_monitor = None
            logging.info("LED日志监控已停止")


# 全局WebSocket服务器实例
simple_websocket_server = SimpleWebSocketServer()
