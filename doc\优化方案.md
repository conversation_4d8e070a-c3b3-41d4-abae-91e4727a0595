## 优化方案（视觉检测项目）

### 目标
- 稳定：在可见/最小化/后台状态下保持稳定帧率与响应
- 实时：降低端到端延迟与抖动，保障“最新帧”实时显示与检测
- 可观测：关键指标可视化（Processing/Display FPS、丢帧率、耗时分布）
- 可维护：日志可控、配置有版本、模块边界清晰

### 当前架构简述（已完成）
- 主线程：GUI（cv2.imshow + cv2.waitKey），读键写 SharedState
- 处理线程：ProcessingWorker 采集+检测+prepare_display_frame → display_queue(maxsize=1)
- 高分辨率计时器：Windows 下启用 1ms（timeBeginPeriod），退出自动恢复
- display_thread.py：保留但默认关闭（USE_DISPLAY_THREAD=False）

---

## 一、必须优化（短期落地）

### 1. 外部 IO（HTTP/WS/CPU 信号）异步化并限时
问题：网络超时可能阻塞主循环/处理线程，降低整体吞吐。
方案：
- 所有外部 IO 丢到 async_task_manager 或线程池执行
- 设置超时、重试（指数退避）、熔断；主线仅投递任务
- 结果通过回调/队列返回，不阻塞检测线程
验收：检测循环平均耗时不受网络抖动影响；网络错误仅以事件日志体现

### 2. SharedState 写入统一加锁
问题：主线程直接写 last_key/should_exit，虽概率低但存竞争风险。
方案：
- SharedState 增加 set_last_key()/request_exit() 方法；所有写入经锁
- 主线程、显示线程（若启用）统一改用封装方法

示例：
<augment_code_snippet path="AA05取消红色灯珠重构01/display_thread.py" mode="EXCERPT">
````python
def request_exit(self):
    with self.lock:
        self.should_exit = True
````
</augment_code_snippet>
问题：网络超时阻塞主循环/处理线程，降低整体吞吐。
方案：
- 所有外部 IO 丢到 async_task_manager 或线程池执行
- 设置超时、重试（指数退避）、熔断；主线仅投递任务
- 结果通过回调/队列返回，不阻塞检测线程
验收：检测循环平均耗时不受网络抖动影响；网络错误仅以事件日志体现

### 3. SharedState 写入统一加锁
问题：主线程直接写 last_key/should_exit，虽概率低但存竞争风险。
方案：
- SharedState 增加 set_last_key()/request_exit() 方法；所有写入经锁
- 主线程、显示线程（若启用）统一改用封装方法

示例：
<augment_code_snippet path="AA05取消红色灯珠重构01/display_thread.py" mode="EXCERPT">
````python
def request_exit(self):
    with self.lock:
        self.should_exit = True
````
</augment_code_snippet>

---

## 二、优先优化（中期高收益）

### 4. 三线程架构：采集线程 + 检测线程 + GUI 主线程
现状：cap.read() 在检测路径中，检测抖动会影响采集节拍。
方案：
- 新增 CaptureWorker：cap.read() → frame_queue(maxsize=1)
- ProcessingWorker：从 frame_queue 取“最新帧”做检测 → display_queue
- 主线程：仅从 display_queue 取并显示
收益：采集节奏稳定，检测抖动与显示完全解耦

伪代码：
```
CaptureWorker: while running: frame = cap.read(); put_nowait(frame) else drop_old
ProcessingWorker: frame = frame_queue.get_latest(); process(); put display_frame
Main: display_frame = display_queue.get_latest(); imshow + waitKey
```

### 5. HUD/指标可视化
- HUD 展示：Processing FPS、Display FPS、丢帧率（队列被替换计数/总帧）、检测耗时均值/95分位
- WebSocket 周期上报精简指标，方便远程监控

示例（HUD 增加 Display FPS）：
<augment_code_snippet path="AA05取消红色灯珠重构01/ui_handler.py" mode="EXCERPT">
````python
cv2.putText(app_state.display_frame,
            f"DispFPS: {app_state.shared_state.display_fps:.1f}",
            (w - 280, 60), cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0,255,0), 1)
````
</augment_code_snippet>

### 6. 绘制与内存分配优化
- 静态元素（标题/帮助）使用半透明 overlay，减少每帧重绘
- 复用中间缓冲，避免大对象反复创建
- ROI 绘制采用“脏矩形”策略：仅变动时重绘

### 7. 摄像头后端/参数 Profile 化
- 对比 CAP_DSHOW 与 CAP_MSMF，记录不同设备在后台/最小化时的节流差异
- 将分辨率/曝光/亮度/后端等抽为 profile，便于一键切换与回滚

---

## 三、可选优化（长期提质）

### 8. 算法稳健性
- 利用已有 led_history/led_last_stable_status 做更稳的状态滤波/滞回
- 自适应亮度/背景参考（利用 ambient_* 预留字段），降低环境光影响
- 段识别容错规则：常见混淆（8/9/0/6 等）启发式二判

### 9. 计算性能优化
- 将阈值/ROI统计尽量向量化（NumPy），减少 Python 循环
- 小热点考虑 Numba/Cython；必要时评估 OpenCV CUDA/UMat（注意 Win 兼容）

### 10. 稳定性与恢复
- 相机看门狗：cap.read() 连续失败自动软重启相机
- 异常快照：保存最近 N 秒关键指标与状态，便于线下复盘

### 11. 配置与测试
- combined_config.json 增加 schema_version，提供迁移逻辑
- 单元/集成测试：
  - ROI 序列化/反序列化一致性
  - 阈值计算正确性
  - 队列“丢旧留新”策略正确性
  - SharedState 并发读写一致性

---

## 四、实施路线图（建议顺序）
1) 必须优化：外部 IO 异步化 + SharedState 写封装
2) 三线程架构落地（新增 CaptureWorker）
3) HUD 指标与 WebSocket 上报（FPS/丢帧/耗时分布）
4) 绘制/内存优化与 profile 化（MSMF/DSHOW 切换）
5) 长期优化：算法稳健性与计算加速、看门狗、配置与测试完善

---

## 五、验收指标（示例）
- 稳定性：最小化/可见状态 Processing FPS 差异 < 5%
- 实时性：display_queue 丢帧率 < 10%，端到端延迟 < 2 帧
- 性能：检测循环 p95 耗时 < 3ms（按当前分辨率与算法）
- 可观测：HUD 同时展示 Processing/Display FPS；日志仅在异常或按需开启 DEBUG 时密集

---

## 六、涉及文件清单与修改面
- main.py：保留 GUI 主线程；（将来）启动 CaptureWorker；主循环读取 display_queue
- processing_thread.py：仅做检测；（将来）改为从 frame_queue 取原始帧
- 新增 capture_thread.py（建议）：cap.read() → frame_queue(maxsize=1)
- ui_handler.py：HUD 指标；尽量复用绘制缓冲（不更改分析所需的 INFO 日志级别）
- display_thread.py：默认关闭；如需启用，明确其职责仅显示与按键
- high_res_timer.py：已启用 1ms 计时器（Windows）
- async_task_manager.py：承接网络与CPU通信等外部 IO（限时/重试/熔断）

---

## 七、注意事项
- OpenCV GUI 必须在主线程；不要在子线程调用 imshow/waitKey

---

## 八、不影响算法与日志分析的约束（强制）
为确保现有算法检测与 led_digit_detection.log 的离线分析完全不受影响，实施本优化方案时必须遵守以下约束：
- 保持日志协议不变（级别与格式）：
  - 分析所需的两类关键日志必须保持 INFO 级别与现有文案格式不变：
    - 每帧状态快照：`LED G|R#: Status=..., Gray=..., Green=..., Red=..., Brightness=...%`
    - 状态变化事件：`LED G|R# state OFF->ON/ON->OFF, ...`（含“(Filtered)”或“(Raw Threshold)”标记）
  - 不修改 LOG_FORMAT 与时间戳格式，确保正则解析稳定。
- FileHandler 生命周期维持现状：仅在 '88' 触发后的 LOGGING 窗口内动态添加；结束后移除。
- 不调整 logger 全局级别以致影响上述 INFO 行的写入；不得将关键 INFO 行降级为 DEBUG。
- 三线程改造（采集线程）须保证：
  - 日志仍由处理线程在检测完成后统一写出；
  - 队列容量为 1、策略为“丢旧留新”，避免积压导致时间错位；
  - 日志行的产生频率可能变化，但 analyzer 使用 ±2s 时间窗口，仍可正确聚合。
- 外部 IO 异步化只改变耗时路径，不改动日志写入路径与内容。
- 如需调整检测算法（阈值/滤波/容错），必须先用 testdoc 下的样本日志与数据回放，验证 analyze_led_log 的结果不变或在可接受范围内（并更新文档说明）。

- 队列均使用 maxsize=1，非阻塞 put；始终“丢旧留新”
- 对共享状态的写操作统一封装并加锁，避免偶发竞争
- 日志以场景为先：默认安静，调试时可动态提升等级

