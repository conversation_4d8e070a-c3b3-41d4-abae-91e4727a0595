"""
Processing thread: run capture + processing in background, GUI stays in main thread
"""
import time
import queue
import logging
import cv2  # only for type hints if needed, not used directly

import ui_handler

class ProcessingWorker:
    """Background worker that performs detection and prepares display frames.
    It pushes frames into app_state.display_queue (maxsize=1)."""
    def __init__(self, app_state):
        self.app_state = app_state
        self.running = False
        self.thread = None

    def start(self):
        if self.thread and self.thread.is_alive():
            return
        import threading
        self.running = True
        self.thread = threading.Thread(target=self._loop, daemon=True)
        self.thread.start()
        logging.info("处理线程已启动")

    def stop(self):
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        logging.info("处理线程已停止")

    def _loop(self):
        app_state = self.app_state
        while self.running and not app_state.shared_state.should_exit:
            # Core processing (capture + detection)
            ui_handler.process_core_logic(app_state)

            # Prepare display frame
            display_frame = ui_handler.prepare_display_frame(app_state)

            # Non-blocking push, keep latest frame
            # --- 非阻塞方式放入队列，实现真解耦 ---
            # 核心逻辑：永不等待。如果队列满了，就先扔掉旧的，再放入新的。
            try:
                # 尝试立即放入，如果队列未满，这是最高效的方式
                self.app_state.display_queue.put_nowait(display_frame)
            except queue.Full:
                # 如果队列满了，说明消费者（主线程）处理不过来
                try:
                    # 立即从队列中取出一个旧的元素，腾出空间
                    _ = self.app_state.display_queue.get_nowait()
                except queue.Empty:
                    # 在极罕见的并发情况下，可能刚判断满，就被消费了
                    # 此时忽略即可，因为下面的 put_nowait 会成功
                    pass
                # 再次尝试放入，这次几乎肯定会成功
                try:
                    self.app_state.display_queue.put_nowait(display_frame)
                except queue.Full:
                    # 在极端的并发竞争下，如果还满，就放弃这一帧
                    # 这保证了处理线程绝对不会被阻塞
                    logging.warning("Display queue is still full after attempting to clear. Frame dropped.")
                    pass

            # ------------------------------------

            # 更新共享状态（如果需要）
            # self.app_state.shared_state.set_last_key(-1)  # 清除按键状态

            # Update processing FPS into shared state
            try:
                app_state.shared_state.set_processing_fps(app_state.fps)
            except Exception:
                pass

            # A tiny sleep to yield (optional)
            # time.sleep(0)  # Let scheduler breathe

