#!/usr/bin/env python3
"""
WebSocket服务器测试脚本
用于验证WebSocket服务器是否能正常启动和运行
"""

import time
import logging
from simple_websocket_server import simple_websocket_server

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_websocket_server():
    """测试WebSocket服务器"""
    print("=== WebSocket服务器测试 ===")
    
    try:
        # 启动WebSocket服务器
        print("1. 启动WebSocket服务器...")
        simple_websocket_server.start_server()
        
        # 等待服务器启动
        time.sleep(2)
        
        if simple_websocket_server.running:
            print("✓ WebSocket服务器启动成功")
            print(f"✓ 服务器地址: ws://{simple_websocket_server.host}:{simple_websocket_server.port}")
        else:
            print("✗ WebSocket服务器启动失败")
            return False
        
        # 测试状态更新
        print("\n2. 测试状态更新...")
        simple_websocket_server.start_detection(10, "测试检测开始")
        print("✓ 检测开始状态已发送")
        
        # 模拟倒计时
        for i in range(10, 0, -1):
            time.sleep(1)
            simple_websocket_server.update_countdown(i, f"测试倒计时: {i}秒")
            print(f"✓ 倒计时更新: {i}秒")
        
        # 设置分析状态
        simple_websocket_server.set_analyzing_state("测试分析中...")
        print("✓ 分析状态已发送")
        time.sleep(2)
        
        # 完成检测
        simple_websocket_server.complete_detection("测试完成")
        print("✓ 检测完成状态已发送")
        
        # 等待一段时间让客户端接收消息
        print("\n3. 保持服务器运行30秒，可以用浏览器测试连接...")
        print("   打开浏览器控制台，运行以下代码测试连接：")
        print("   const ws = new WebSocket('ws://localhost:8766');")
        print("   ws.onopen = () => console.log('连接成功');")
        print("   ws.onmessage = (e) => console.log('收到消息:', JSON.parse(e.data));")
        
        for i in range(30, 0, -1):
            print(f"\r   剩余时间: {i}秒", end="", flush=True)
            time.sleep(1)
        
        print("\n\n4. 停止WebSocket服务器...")
        simple_websocket_server.stop_server()
        print("✓ WebSocket服务器已停止")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        logging.error(f"WebSocket测试错误: {e}")
        return False
    finally:
        # 确保服务器被停止
        try:
            simple_websocket_server.stop_server()
        except:
            pass

if __name__ == "__main__":
    success = test_websocket_server()
    if success:
        print("\n=== 测试完成：WebSocket服务器工作正常 ===")
    else:
        print("\n=== 测试失败：WebSocket服务器存在问题 ===")
