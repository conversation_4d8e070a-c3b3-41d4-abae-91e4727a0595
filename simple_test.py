"""
简单测试脚本 - 验证幽灵窗口修复
"""
import sys
import time
import cv2
import numpy as np

# 添加项目路径
sys.path.append('.')
from ghost_window import GhostWindow

def simple_test():
    """简单测试幽灵窗口是否影响主UI"""
    print("=== 简单测试 ===")
    print("测试幽灵窗口是否会影响主UI界面的正常显示")
    
    # 创建主窗口
    cv2.namedWindow("MainUI", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("MainUI", 640, 480)
    
    # 创建幽灵窗口
    ghost_win = GhostWindow("SimpleGhost")
    ghost_win.start()
    
    try:
        print("测试开始，按 ESC 键退出...")
        
        frame_count = 0
        start_time = time.time()
        
        while True:
            # 创建一个简单的测试帧
            test_frame = np.full((480, 640, 3), (64, 64, 64), dtype=np.uint8)
            
            # 添加一些动态内容
            x = int(320 + 200 * np.sin(frame_count * 0.05))
            y = int(240 + 200 * np.cos(frame_count * 0.05))
            cv2.circle(test_frame, (x, y), 20, (0, 255, 0), -1)
            
            # 显示主窗口
            cv2.imshow("MainUI", test_frame)
            
            # 更新幽灵窗口
            ghost_win.update_in_main_thread()
            
            # 处理按键
            key = cv2.waitKey(30) & 0xFF
            if key == 27:  # ESC
                break
            
            frame_count += 1
            
            # 每秒输出一次状态
            if frame_count % 33 == 0:
                fps = frame_count / (time.time() - start_time)
                print(f"主UI运行正常，FPS: {fps:.1f}")
        
        print("测试完成")
        
    finally:
        ghost_win.stop()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    simple_test()