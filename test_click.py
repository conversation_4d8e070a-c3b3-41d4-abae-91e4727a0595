"""
测试自动点击功能
"""
import time
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_click_functionality():
    """测试点击功能"""
    print("=" * 60)
    print("调试幽灵窗口 - 点击功能测试")
    print("=" * 60)
    
    if sys.platform != 'win32':
        print("❌ 当前平台不是Windows，自动点击功能不可用")
        return
    
    try:
        # 导入Windows API
        import ctypes
        from ctypes import wintypes
        
        # 定义常量
        MOUSEEVENTF_LEFTDOWN = 0x0002
        MOUSEEVENTF_LEFTUP = 0x0004
        
        # 定义结构体
        class POINT(ctypes.Structure):
            _fields_ = [("x", ctypes.c_long), ("y", ctypes.c_long)]
        
        user32 = ctypes.windll.user32
        
        print("✅ Windows API 导入成功")
        
        # 获取当前鼠标位置
        current_pos = POINT()
        user32.GetCursorPos(ctypes.byref(current_pos))
        print(f"📍 当前鼠标位置: ({current_pos.x}, {current_pos.y})")
        
        # 测试点击
        print("\n🖱️  测试鼠标点击功能...")
        print("将在3秒后在当前位置执行点击...")
        
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        
        # 执行点击
        user32.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
        time.sleep(0.05)
        user32.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        
        print("✅ 点击测试完成！")
        print("\n如果看到了点击效果，说明Windows API调用正常。")
        print("现在可以运行 debug_ghost_window.py 测试完整功能。")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_click_functionality()