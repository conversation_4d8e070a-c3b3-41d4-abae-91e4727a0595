"""
UI Drawing Components
包含所有与绘制相关的函数：ROI绘制、HUD显示等
"""

import cv2
import numpy as np
import time
import logging
from app_state import AppState
from constants import *


def draw_rois(app_state: AppState):
    """在 display_frame 上绘制所有相关的 ROI"""
    if app_state.display_frame is None: return

    # --- 在校准模式下绘制 ---
    if app_state.current_mode == MODE_CALIBRATION:
        # 基准点选择
        if app_state.current_calib_state == CALIB_STATE_BASE_POINTS_SELECT:
            # 绘制已选择的基准点
            for i, point in enumerate(app_state.base_points):
                if point:
                    x, y = point
                    # 绘制基准点标记
                    cv2.circle(app_state.display_frame, (x, y), 8, (0, 255, 255), 2)  # 黄色圆圈
                    cv2.circle(app_state.display_frame, (x, y), 2, (0, 0, 255), -1)   # 红色中心点
                    # 绘制基准点编号
                    cv2.putText(app_state.display_frame, f"P{i+1}",
                               (x + 12, y - 8), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                    # 绘制模板区域边框
                    half_size = app_state.base_template_size // 2
                    cv2.rectangle(app_state.display_frame,
                                 (x - half_size, y - half_size),
                                 (x + half_size, y + half_size),
                                 (255, 255, 0), 1)  # 青色模板边框
        # LED ROI 选择
        if app_state.current_calib_state == CALIB_STATE_LED_ROI_SELECT:
            # 绘制已确认的 ROI
            for i, roi in enumerate(app_state.led_rois):
                if roi:
                    is_green = i < app_state.led_num_green
                    label = f"G{i+1}" if is_green else f"R{i - app_state.led_num_green + 1}"
                    color = (0, 255, 0) if is_green else (0, 0, 255) # Green/Red for confirmed
                    if i >= app_state.calib_led_roi_index: # 未确认的用橙色
                         color = (0, 165, 255)
                    cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), color, 1)
                    text_y = roi[1] - 5 if roi[1] > 10 else roi[1] + roi[3] + 15
                    cv2.putText(app_state.display_frame, label, (roi[0], text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            # 绘制正在选择的 ROI (蓝色)
            if app_state.current_rect:
                r = app_state.current_rect
                cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (255, 0, 0), 1)

            # 绘制模板预览 (虚线边框，最小遮挡)
            if (app_state.template_mode or app_state.fixed_template_mode) and app_state.template_preview_pos:
                r = app_state.template_preview_pos
                x, y, w, h = r

                # 绘制虚线边框 - 最小化遮挡内容
                dash_length = 8  # 虚线段长度
                gap_length = 6   # 间隔长度
                # 固定尺寸模板用不同颜色区分
                color = (0, 255, 255) if app_state.fixed_template_mode else (255, 255, 255)  # 黄色 vs 白色
                thickness = 1

                # 绘制顶边虚线
                for i in range(0, w, dash_length + gap_length):
                    end_x = min(x + i + dash_length, x + w)
                    cv2.line(app_state.display_frame, (x + i, y), (end_x, y), color, thickness)

                # 绘制底边虚线
                for i in range(0, w, dash_length + gap_length):
                    end_x = min(x + i + dash_length, x + w)
                    cv2.line(app_state.display_frame, (x + i, y + h), (end_x, y + h), color, thickness)

                # 绘制左边虚线
                for i in range(0, h, dash_length + gap_length):
                    end_y = min(y + i + dash_length, y + h)
                    cv2.line(app_state.display_frame, (x, y + i), (x, end_y), color, thickness)

                # 绘制右边虚线
                for i in range(0, h, dash_length + gap_length):
                    end_y = min(y + i + dash_length, y + h)
                    cv2.line(app_state.display_frame, (x + w, y + i), (x + w, end_y), color, thickness)

        # LED ROI 编辑模式
        elif app_state.current_calib_state == CALIB_STATE_LED_EDIT:
            # 绘制所有ROI
            for i, roi in enumerate(app_state.led_rois):
                if roi:
                    is_green = i < app_state.led_num_green
                    label = f"G{i+1}" if is_green else f"R{i - app_state.led_num_green + 1}"

                    # 根据是否选中决定颜色和样式
                    if i == app_state.selected_roi_index:
                        # 选中的ROI用橙色高亮显示（与标记阶段相同的线宽）
                        color = (0, 165, 255)  # 橙色
                        thickness = 1
                        # 绘制选中标记（四个角的小方块）
                        corner_size = 8
                        x, y, w, h = roi
                        corner_color = (255, 255, 0)  # 黄色角标
                        # 四个角（完全绘制在 ROI 内部，避免看起来比原始ROI更大）
                        cv2.rectangle(app_state.display_frame, (x, y),
                                     (x+corner_size, y+corner_size), corner_color, -1)
                        cv2.rectangle(app_state.display_frame, (x+w-corner_size, y),
                                     (x+w, y+corner_size), corner_color, -1)
                        cv2.rectangle(app_state.display_frame, (x, y+h-corner_size),
                                     (x+corner_size, y+h), corner_color, -1)
                        cv2.rectangle(app_state.display_frame, (x+w-corner_size, y+h-corner_size),
                                     (x+w, y+h), corner_color, -1)
                    else:
                        # 未选中的ROI用正常颜色
                        color = (0, 255, 0) if is_green else (0, 0, 255)  # Green/Red
                        thickness = 1  # 从2减少到1

                    cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), color, thickness)
                    text_y = roi[1] - 5 if roi[1] > 10 else roi[1] + roi[3] + 15
                    cv2.putText(app_state.display_frame, label, (roi[0], text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

                    # 在ROI外侧显示数字键提示（避免遮挡内容）
                    key_hint = str(i + 1)
                    hint_color = (255, 215, 0)  # 金黄
                    # 优先放在ROI上方；若不够空间，放在下方
                    hint_y = roi[1] - 6 if roi[1] > 14 else roi[1] + roi[3] + 14
                    hint_x = roi[0] + 2
                    cv2.putText(app_state.display_frame, key_hint, (hint_x, hint_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, hint_color, 1)

        # LED 样本采集
        elif app_state.current_calib_state in [CALIB_STATE_LED_SAMPLE_OFF, CALIB_STATE_LED_SAMPLE_ON]:
             is_on_state = app_state.current_calib_state == CALIB_STATE_LED_SAMPLE_ON
             for i, roi in enumerate(app_state.led_rois):
                 if roi:
                     is_green = i < app_state.led_num_green
                     # 根据预期状态选择颜色
                     if is_on_state: # 期望 ON
                          color = (0, 255, 0) if is_green else (0, 0, 255) # Bright Green / Bright Red
                     else: # 期望 OFF
                          color = (0, 100, 0) if is_green else (100, 0, 0) # Dark Green / Dark Red
                     cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), color, 2)

        # 数码管 ROI 选择 (Digit 1 or 2)
        elif app_state.current_calib_state in [CALIB_STATE_DIGIT_ROI_SELECT_1, CALIB_STATE_DIGIT_ROI_SELECT_2]:
            # 使用捕捉的 '88' 图像
            if app_state.digit_calibration_image_88 is not None:
                 # 确保 display_frame 是 '88' 图像的副本
                 if not np.array_equal(app_state.display_frame, app_state.digit_calibration_image_88):
                      app_state.display_frame = app_state.digit_calibration_image_88.copy()

                 # 绘制 Digit 1 (绿色框)
                 if app_state.digit_rois[0]:
                      r = app_state.digit_rois[0]
                      cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (0, 255, 0), 1)
                 # 绘制 Digit 2 (如果已定义，用青色框)
                 if app_state.digit_rois[1] and app_state.current_calib_state == CALIB_STATE_DIGIT_ROI_SELECT_2:
                      r = app_state.digit_rois[1]
                      cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (255, 255, 0), 1)
                 # 绘制正在选择的 ROI (蓝色)
                 if app_state.current_rect:
                     r = app_state.current_rect
                     cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (255, 0, 0), 1)

        # 数码管段 ROI 选择
        elif app_state.current_calib_state == CALIB_STATE_DIGIT_SEGMENT_SELECT:
             if app_state.digit_calibration_image_88 is not None:
                 if not np.array_equal(app_state.display_frame, app_state.digit_calibration_image_88):
                      app_state.display_frame = app_state.digit_calibration_image_88.copy()

                 # 绘制已确认的 Digit ROIs (细绿框)
                 for roi in app_state.digit_rois:
                     if roi: cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), (0, 255, 0), 1)

                 # 绘制当前 Digit 已确认的 Segment ROIs
                 d_idx = app_state.calib_digit_index
                 if 0 <= d_idx < NUM_DIGITS:
                     for s_idx, roi in enumerate(app_state.digit_segment_rois[d_idx]):
                         if roi:
                             color = (0, 255, 0) if s_idx < app_state.calib_segment_index else (0, 165, 255) # Green (done) vs Orange (pending)
                             cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), color, 1)

                 # 绘制正在选择的 Segment ROI (蓝色)
                 if app_state.current_rect:
                     r = app_state.current_rect
                     cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (255, 0, 0), 1)

        # 数码管阈值调整
        elif app_state.current_calib_state == CALIB_STATE_DIGIT_ADJUST_THRESHOLD:
            if app_state.digit_background_image_off is not None:
                if not np.array_equal(app_state.display_frame, app_state.digit_background_image_off):
                    app_state.display_frame = app_state.digit_background_image_off.copy()
                try:
                    temp_gray = cv2.cvtColor(app_state.display_frame, cv2.COLOR_BGR2GRAY)
                    for d_idx in range(NUM_DIGITS):
                        if app_state.digit_rois[d_idx]:
                            for s_idx, roi in enumerate(app_state.digit_segment_rois[d_idx]):
                                if roi and isinstance(roi, tuple) and len(roi)==4:
                                    sx, sy, sw, sh = roi
                                    if sw > 0 and sh > 0:
                                        y_end = min(sy + sh, temp_gray.shape[0])
                                        x_end = min(sx + sw, temp_gray.shape[1])
                                        roi_y = max(0, sy)
                                        roi_x = max(0, sx)
                                        if roi_y < y_end and roi_x < x_end:
                                            segment_area = temp_gray[roi_y:y_end, roi_x:x_end]
                                            if segment_area.size > 0:
                                                mean_brightness = cv2.mean(segment_area)[0]
                                                # Red if incorrectly ON, Green if correctly OFF
                                                color = (0, 0, 255) if mean_brightness > app_state.digit_brightness_threshold else (0, 255, 0)
                                                cv2.rectangle(app_state.display_frame, (sx, sy), (sx + sw, sy + sh), color, 1)
                except Exception as e:
                    print(f"绘制阈值调整状态时出错: {e}")

    # --- 在检测模式下绘制 ---
    elif app_state.current_mode == MODE_DETECTION:
        # 1. 绘制 LED ROI 和状态
        for i, roi in enumerate(app_state.led_rois):
            if not roi or not isinstance(roi, tuple) or len(roi) != 4: continue
            x, y, w, h = roi
            if w <= 0 or h <= 0: continue

            is_on = app_state.led_last_status[i] if i < len(app_state.led_last_status) else False
            is_green_led = (i < app_state.led_num_green)
            led_label = f"G{i+1}" if is_green_led else f"R{i - app_state.led_num_green + 1}"
            status_text = "ON" if is_on else "OFF"

            if is_green_led:
                text_color = (0, 255, 0) if is_on else (150, 150, 150)
                border_color = (0, 255, 0) if is_on else (0, 0, 150)
            else: # Red LED
                text_color = (0, 0, 255) if is_on else (150, 150, 150)
                border_color = (0, 0, 255) if is_on else (150, 0, 0)

            cv2.rectangle(app_state.display_frame, (x, y), (x + w, y + h), border_color, 1)
            text_y = y - 5 if y > 10 else y + h + 15
            cv2.putText(app_state.display_frame, f"{led_label}:{status_text}", (x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, text_color, 1)

        # 2. 绘制数码管段 ROI 和状态
        for d_idx in range(NUM_DIGITS):
            if app_state.digit_rois[d_idx] is None: continue
            dx, dy, dw, dh = app_state.digit_rois[d_idx]

            rec_char = app_state.digit_last_recognized_chars[d_idx] if d_idx < len(app_state.digit_last_recognized_chars) and app_state.digit_last_recognized_chars[d_idx] is not None else '?'
            missing = app_state.digit_last_missing_segments[d_idx] if d_idx < len(app_state.digit_last_missing_segments) else []
            status = "OK"
            status_color = (0, 255, 0)
            if missing:
                missing_labels = [DIGIT_SEGMENT_LABELS[s] for s in missing]
                status = f"FAIL({','.join(missing_labels)})"
                status_color = (0, 0, 255)
            elif rec_char == '?':
                status = "Unknown"
                status_color = (0, 165, 255)

            # 显示识别的字符
            cv2.putText(app_state.display_frame, f"{rec_char}", (dx + dw // 2 - 10, dy + dh + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.6, status_color, 2)

            # 绘制每个段的状态
            pattern = app_state.digit_last_segment_patterns[d_idx] if d_idx < len(app_state.digit_last_segment_patterns) else [0]*NUM_SEGMENTS_PER_DIGIT
            for s_idx, seg_roi in enumerate(app_state.digit_segment_rois[d_idx]):
                 if seg_roi and isinstance(seg_roi, tuple) and len(seg_roi)==4:
                     sx, sy, sw, sh = seg_roi
                     if sw <=0 or sh <=0: continue

                     is_seg_on = pattern[s_idx] == 1 if s_idx < len(pattern) else False
                     seg_color = (0, 255, 0) if is_seg_on else (50, 50, 50)
                     thickness = 2 if is_seg_on else 1

                     # 高亮缺失的段
                     is_missing = s_idx in missing
                     if is_missing:
                          seg_color = (0, 255, 255) # Yellow border
                          cv2.rectangle(app_state.display_frame, (sx, sy), (sx + sw, sy + sh), seg_color, 1)
                          cv2.drawMarker(app_state.display_frame, (sx + sw // 2, sy + sh // 2), (0, 0, 255), cv2.MARKER_CROSS, max(5, min(sw, sh)//2), 1)
                     else:
                          cv2.rectangle(app_state.display_frame, (sx, sy), (sx + sw, sy + sh), seg_color, thickness)


def draw_hud(app_state: AppState):
    """在 display_frame 上绘制状态信息和提示（带 HUD 缓存与降频刷新）"""
    if app_state.display_frame is None: return

    h, w = app_state.display_frame.shape[:2]
    info_color = (255, 255, 0) # Yellow

    # 根据窗口大小动态调整字体大小（进一步减小）
    base_font_scale = 0.32
    if w > 1600:  # 高分辨率时稍微增大
        base_font_scale = 0.36
    elif w < 1000:  # 低分辨率时进一步减小
        base_font_scale = 0.28

    font_scale = base_font_scale
    led_font_scale = base_font_scale - 0.05  # LED状态文本稍小
    line_height = 15  # 行高更紧凑，容纳更多内容

    # 计算是否需要刷新 HUD（约 10Hz）
    now_ts = time.time()
    need_refresh = (
        app_state.hud_canvas is None or
        app_state.hud_mask is None or
        app_state.hud_canvas.shape[:2] != (h, w) or
        (now_ts - app_state.hud_last_update_ts) >= getattr(app_state, 'hud_refresh_interval', 0.1)
    )

    if need_refresh:
        # 重新生成 HUD 图层（透明背景）
        hud = np.zeros_like(app_state.display_frame)
        hud_mask = np.zeros((h, w), dtype=np.uint8)

        y_offset = 15

        # --- 左上：Digits 区块（标题为静态元素） ---
        if app_state.current_mode == MODE_DETECTION:
            cv2.putText(hud, "--- Digits ---", (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, font_scale, info_color, 1)
            cv2.putText(hud_mask, " ", (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, font_scale, 255, 1)  # 占位，方便掩膜构建
            y_offset += line_height

            # 合并 D1/D2 为一行显示
            digit_items = []
            overall_status = "OK"
            for d_idx in range(NUM_DIGITS):
                if app_state.digit_rois[d_idx] is None:
                    continue  # 只显示已定义的数字
                rec_char = app_state.digit_last_recognized_chars[d_idx] if d_idx < len(app_state.digit_last_recognized_chars) and app_state.digit_last_recognized_chars[d_idx] is not None else '?'
                missing = app_state.digit_last_missing_segments[d_idx] if d_idx < len(app_state.digit_last_missing_segments) else []
                if missing:
                    labels = ','.join(DIGIT_SEGMENT_LABELS[s] for s in missing)
                    digit_items.append(f"D{d_idx+1}={rec_char} FAIL({labels})")
                    overall_status = "FAIL"
                elif rec_char == '?':
                    digit_items.append(f"D{d_idx+1}=? Unknown")
                    if overall_status != "FAIL":
                        overall_status = "Unknown"
                else:
                    digit_items.append(f"D{d_idx+1}={rec_char} OK")

            combined_text = "D: " + " ".join(f"[{it}]" for it in digit_items) if digit_items else "D: [N/A]"
            color = (0, 255, 0) if overall_status == "OK" else ((0, 165, 255) if overall_status == "Unknown" else (0, 0, 255))
            cv2.putText(hud, combined_text, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, color, 1)
            y_offset += line_height

            # 数码管阈值显示
            cv2.putText(hud, f"D Th (+/-): {app_state.digit_brightness_threshold:.1f}", (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, info_color, 1)
            y_offset += line_height

        # --- 左上角信息区 ---
        # LED 状态
        if app_state.current_mode == MODE_DETECTION and app_state.led_max_rois > 0:
            cv2.putText(hud, "--- LEDs ---", (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, font_scale, info_color, 1)
            y_offset += line_height
            status_texts_led = []
            for i in range(app_state.led_max_rois):
                 if not app_state.led_rois[i]: continue # 只显示已定义的 ROI
                 is_on = app_state.led_last_status[i] if i < len(app_state.led_last_status) else False
                 values = app_state.led_last_values[i] if i < len(app_state.led_last_values) else (0,0,0)
                 gray_val, green_val, red_val = values
                 is_green_led = (i < app_state.led_num_green)
                 led_label = f"G{i+1}" if is_green_led else f"R{i - app_state.led_num_green + 1}"
                 status_text = "ON" if is_on else "OFF"
                 # 优化文本格式：缩短标签，减少小数位数
                 status_texts_led.append(f"{led_label}: {status_text} (Gy:{gray_val:.0f},Gn:{green_val:.0f},Rd:{red_val:.0f})")

            for txt in status_texts_led:
                if y_offset > h - 80: break  # 增加底部边距
                is_green_led = txt.startswith("G")
                color = (255, 255, 255)
                if ": ON" in txt: color = (0, 255, 0) if is_green_led else (0, 0, 255)
                elif ": OFF" in txt: color = (150, 150, 150)
                cv2.putText(hud, txt, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, color, 1)
                y_offset += line_height

            # LED 阈值显示 (检测模式下) - 分行显示以避免过长
            # 绿色LED阈值
            thresh_text_g = f"G Th: G(g/G)={app_state.led_gray_threshold_green:.1f},Gn(v/V)={app_state.led_green_threshold:.1f}"
            cv2.putText(hud, thresh_text_g, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, info_color, 1)
            y_offset += line_height
            # 红色LED阈值
            thresh_text_r = f"R Th: G(y/Y)={app_state.led_gray_threshold_red:.1f},Rd(r/R)={app_state.led_red_threshold:.1f}"
            cv2.putText(hud, thresh_text_r, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, info_color, 1)
            y_offset += line_height
            # G33特殊LED阈值
            thresh_text_g33 = f"G33 Th: G(3/#)={app_state.led_gray_threshold_g33:.1f},Gn(e/E)={app_state.led_green_threshold_g33:.1f}"
            cv2.putText(hud, thresh_text_g33, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, info_color, 1)
            y_offset += line_height + 3

        # --- 右上角模式和 FPS ---
        mode_map = {MODE_CAMERA_SETTINGS: "Settings", MODE_CALIBRATION: "Calibration", MODE_DETECTION: "Detection"}
        mode_str = f"Mode: {mode_map.get(app_state.current_mode, 'Unknown')}"
        if app_state.current_mode == MODE_CALIBRATION:
             mode_str += f" (State: {app_state.current_calib_state})" # 显示校准子状态
        cv2.putText(hud, mode_str, (w - 280, 20), cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 0), 1)

        # --- FPS 和置顶状态在同一行 ---
        fps_text = f"FPS: {app_state.fps:.1f}"
        cv2.putText(hud, fps_text, (w - 280, 40), cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), 1)

        # 计算FPS文本宽度，在其右侧显示置顶状态
        fps_text_size = cv2.getTextSize(fps_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
        topmost_x = w - 280 + fps_text_size[0] + 20  # FPS文本右侧 + 20像素间距

        topmost_text = "Topmost: ON" if app_state.window_topmost else "Topmost: OFF"
        topmost_color = (0, 255, 0) if app_state.window_topmost else (128, 128, 128)
        cv2.putText(hud, topmost_text, (topmost_x, 40), cv2.FONT_HERSHEY_SIMPLEX, font_scale, topmost_color, 1)

        # --- 底部状态栏和提示栏 ---
        bottom_font_scale = led_font_scale  # 使用较小的字体

        # 异步任务进度信息（如果有的话）
        if app_state.async_task_progress:
            cv2.putText(hud, app_state.async_task_progress, (10, h - 55), cv2.FONT_HERSHEY_SIMPLEX, bottom_font_scale, (255, 165, 0), 1)  # 橙色

        # 状态消息 (根据模式显示不同内容，或者直接显示 app_state.status_message)
        if app_state.status_message:
             cv2.putText(hud, app_state.status_message, (10, h - 35), cv2.FONT_HERSHEY_SIMPLEX, bottom_font_scale, (0, 255, 255), 1)
        # 提示消息
        if app_state.prompt_message:
             cv2.putText(hud, app_state.prompt_message, (10, h - 15), cv2.FONT_HERSHEY_SIMPLEX, bottom_font_scale, (255, 255, 255), 1)

        # 构建掩膜并缓存
        try:
            gray_hud = cv2.cvtColor(hud, cv2.COLOR_BGR2GRAY)
            hud_mask = (gray_hud > 0).astype(np.uint8) * 255
        except Exception:
            hud_mask = np.zeros((h, w), dtype=np.uint8)

        app_state.hud_canvas = hud
        app_state.hud_mask = hud_mask
        app_state.hud_last_update_ts = now_ts

    # 叠加 HUD 到当前帧（仅在掩膜处覆盖）
    if app_state.hud_canvas is not None and app_state.hud_mask is not None:
        try:
            mask_bool = app_state.hud_mask.astype(bool)
            app_state.display_frame[mask_bool] = app_state.hud_canvas[mask_bool]
        except Exception as e:
            logging.debug(f"HUD overlay failed: {e}")