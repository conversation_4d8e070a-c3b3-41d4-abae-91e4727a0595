# 分析检测精度影响因素
_Exported on 2025/8/13 at GMT+8 15:05:10 from Cursor (1.2.2)_

---

**User**

@led_detector.py 我感觉当前的检测精度容易受到标注位置和光照的变化影响，你仔细分析相关代码，思考有没有什么合适的方法去解决这个问题。你只用思考方案、暂时不修改代码

---

**Cursor**

下面列出我对 `led_detector.py` 现有算法的观察，以及针对“ROI 轻微偏移 + 环境光变化”这两个主要痛点可以考虑的改进方向。所有方案都按“改动成本”由低到高排列，便于后续按需取舍并保持“最小修改”原则。  

1. ROI 内部亮度统计方式优化  
   现状：第 261 行直接对整块 ROI 求均值，边缘或背景像素一旦混入就会稀释 LED 真实亮度。  
   改进：  
   • 仅取 ROI 中最亮 N% 的像素，再计算均值或中值；常用区间 5%–15%。  
   • 或先高斯模糊，再取单点极大值而非均值。  
   这样即使 ROI 稍有偏移，只要亮斑仍在窗口内，统计值仍足够高，且对环境整体亮度不敏感。  
   代码改动点：`avg_color_bgr = …` 之前插入一个掩膜或 `np.percentile` 过滤即可，代价极低。

2. 使用颜色比值而非绝对阈值  
   现状：绿色 LED 依赖 G 通道绝对值 + 灰度阈值 (第 274–280 行)。如果整体曝光降低，两者均同步下降，容易误判灭灯。  
   改进：  
   • 绿色：判断 `G / (R+B+1e-6)` 是否高于某阈值；  
   • 红色：判断 `R / (G+B+1e-6)`；  
   • 或者 `S = max(R,G,B) - min(R,G,B)` 作为饱和度。  
   比值天然抵消亮度变化，只受色彩纯度影响，适用于光照强弱波动但色温相对恒定的场景。  
   阈值可以在采样阶段自动学习，比绝对阈值稳定许多。

3. 动态阈值随帧亮度自适应  
   现状：阈值一次性由“灭/亮”样本计算后固定。若灯板附近有额外光源变化，阈值容易老化。  
   改进思路：  
   • 每 N 帧统计一个“环境亮度系数” \(k = \text{mean(gray ROI 全局)} / \text{采样期 mean}\)，随后把灰度阈值按 \(k\) 等比例放大或缩小。  
   • 也可对某个固定“背景参考 ROI”做同样处理。  
   • 阈值更新仅在变化幅度超过±5 %时生效，防止抖动。  
   这样不必重新采样就能跟随环境缓慢漂移，改动点集中在阈值比较（274–280 行）。

4. ROI 微调搜索（亚像素/小窗口平移）  
   ROI 可能因相机抖动、支架热胀冷缩而偏 1–3 px。  
   简易做法：  
   • 在当前 ROI 周围 ±2 px 范围网格搜索灰度最大的位置，把 ROI 中心做一次性修正；  
   • 或者每帧在 ROI 内做模板匹配（耗时更多）。  
   只需在采样阶段或每隔若干帧执行一次，小窗口搜索开销可控。

5. 时间域滤波再加强  
   目前绿色 LED 已有 5 帧平滑 (LED_HISTORY_SIZE)。若仍抖动，可：  
   • 增加窗口到 7–9 帧；  
   • 使用中值滤波而非计数阈值；  
   • 对红色 LED 也做简单的 3 帧去抖。  
   这是纯逻辑调整，不增加计算量。

6. 相机侧措施  
   • 固定曝光、ISO、白平衡，禁止自动模式；  
   • 若硬件允许，开启 HDR 或多曝光融合；  
   • 加装柔光罩或偏振片减小反射。  
   这些是外部手段，但常常最直接、最稳定。

7. 深度学习轻量分类（成本高，暂做远期备选）  
   收集若干 ROI Patch，训练一个极简 CNN/MLP 做二分类，“亮 / 灭”。边界模糊、光照变化都能学习到。  
   但需要数据集和推理框架，引入依赖多，不符合“最小修改”优先级。

总结推荐顺序：  
① 采样最亮 N% 像素 + ② 颜色比值阈值 → ③ 简单亮度系数自适应 → ④ ROI 微调搜索。  
这些改动都可局部实现，不改整体框架，且与现有阈值学习、历史滤波逻辑兼容。

---

**User**

 采样最亮 N% 像素  是不是就是内缩ROI像素取值?颜色比值阈值和当前的检测算法相比准确度高嘛？

---

**Cursor**

1. “采样最亮 N % 像素”≠“简单内缩 ROI”  
   • 内缩 ROI：直接把框子向内收窄若干像素，仍然对框内所有像素做平均。优点是实现最简单，但一旦灯斑在边缘，可能被裁掉而出现误判。  
   • 采样最亮 N %：先在原 ROI 里找出亮度（或某通道值）最高的前 N % 像素，再只对这部分像素做均值/中值运算——相当于用“自适应的亮点掩膜”。这样即使 LED 稍微偏移或 ROI 略大，统计结果仍聚焦在真正的发光区域，对背景像素天然有抑制效果。所以两者思路不同：一个靠“几何裁剪”，一个靠“亮度筛选”，后者对轻微位移、背景干扰更稳健。

2. 颜色比值阈值与当前绝对阈值的准确度对比  
   • 目前算法主要比较“灰度 ≥ 灰度阈值”或“绿色通道 ≥ G 阈值 / 红通道 ≥ R 阈值”。这种绝对值法在环境光或曝光整体变亮/变暗时，阈值必须跟着重设，否则易误判。  
   • 颜色比值（例：G / (R + B) 或 R / (G + B)）则把“彩色纯度”作为指标，基本抵消了整体亮度变化：  
     – 环境光加强 → R、G、B 同时升高，比例近似保持；  
     – 灯亮起 → 目标通道大幅升高，分子增幅远大于分母，比例明显上升。  
   在实际测试中，只要光源色温变化不大，比值阈值对“亮/灭”区分度通常高于灰度阈值，对自动曝光/帧间亮度波动尤其友好。  

   可能的弱点：  
   • 如果背景自身带有接近 LED 色彩的反光，纯比值可能把“假亮点”也判成 ON；  
   • 相机白平衡剧变或光源色温飘移时，G:R:B 比例也会随之偏移，需要稍微调宽阈值或启用灰度兜底。

   综合经验做法：  
   ① 用比值阈值做第一道判断（抗光强漂移）；  
   ② 再用一个较宽松的灰度/通道绝对阈值兜底过滤极暗噪声；  
   ③ 或者在绿色 LED 上要求“(G ≥ G_min) AND (G / (R + B) ≥ ratio_th)”。  
   这样既兼顾亮度波动，也降低色温极端变化时的误检风险。

因此，采样最亮 N % 像素和颜色比值阈值都能在不大改结构的前提下提高对 ROI 偏移与光照变化的鲁棒性；若再配合一次自动采样学习阈值，准确率通常可比现在的纯绝对值方案提升一个档次。

---

**User**

我还是 没太明白这个自动采样学习阈值、会不会造成本来是灭或者亮的，它自动识别错误、反而造成错误的取值判断？

---

**Cursor**

先把“自动采样学习阈值”拆解成两个层级，然后说明各自可能的误判风险与对策，你就能看到它其实可以做到“可控、可撤销、可回滚”，不会随意把阈值学坏。  

────────────────────────────────────────  
一、离线式“双阶段采样”──最稳妥的做法  
1. 明确阶段  
   • 灭灯采样（OFF）：人为/程序确保所有 LED 均熄灭，采 N 帧。  
   • 亮灯采样（ON）：随后让全部 LED 点亮，再采 N 帧。  
   这两个阶段都由用户或上层流程显式触发，互不混淆。  

2. 风险与防护  
   • 风险：采样过程中某几帧 LED 没完全灭或背景反光异常 → 导致阈值被拉高/拉低。  
   • 防护策略  
     a. 大量帧 + 中值/分位数：例如 20 帧取 **第 75% 分位** 而非均值，可自动过滤偶发亮点。  
     b. 异常检测：若同一 ROI 的亮、灭样本差值 < 5 Gray，则提示“样本过于接近，采样失败”。  
     c. 日志/可视化回显：采样完把统计结果打印或在 UI 中显示，让用户确认“灭灯灰度≈20，亮灯≈110”等直观数值。  
   因为采样时 LED 状态是**人为已知**，不依赖算法判断，所以不会出现“算法先误判→再把阈值学坏”的闭环。

────────────────────────────────────────  
二、在线式“渐进式自适应”──用来抵消环境慢漂移  
1. 思路  
   • 阈值初始来自双阶段采样。  
   • 运行过程中，**不**直接根据 ON/OFF 判定来调整阈值，而是引入一个“场景亮度系数” \(k\)：  
     k = 当前一段时间全帧平均灰度 ÷ 采样期平均灰度  
   • 仅用 k 对灰度阈值线性缩放，颜色比值阈值保持不动。  

2. 风险与防护  
   • 风险：出现局部强光照只照到 LED 板，导致 ROI 灰度涨幅 ≠ 全帧灰度涨幅 → k 低估。  
   • 防护策略  
     a. 选取“背景参考 ROI”而非整帧均值（例如板子周围黑框区域）。  
     b. 设置调整上下限：阈值最大只允许增减 ±15%。  
     c. 调整频率：每 100 帧才计算一次 k，且若 |k − 1| < 0.03 则忽略，避免抖动。  
   这里完全**不**依赖当前算法对 LED 的亮灭判断，只是对“整体亮度”做比例修正，因此同样不会把误判反向写入阈值。

────────────────────────────────────────  
三、再谈“会不会学错”的核心逻辑  
• 真正会导致阈值学错的场景，是把“算法自己判断的结果”又拿来反哺阈值（闭环正反馈）。  
• 上述两级方案都规避了这一点：采样阶段 LED 状态由外部保证；在线自适应只看环境亮度，不看 LED 判定。  
• 即使用户误操作（在 OFF 阶段有灯未灭）：  
  – 统计检验会发现亮/灭样本差值过小，触发失败提示；  
  – 用户可重新采样或恢复默认阈值，不会直接写入错误值。  

────────────────────────────────────────  
四、实战推荐流程  
1. 工程上线前：执行一次离线双阶段采样，得到稳健的初始阈值。  
2. 生产运行：  
   • 开启在线亮度系数自适应，缓慢随光强漂移。  
   • 日志持续输出 k 值和实时灰度，便于运维确认。  
3. 若环境发生大幅变化（相机、灯板重新安装）：只需重新跑一次双阶段采样。

这样组合后，系统既能适应日常光照变化，又不会因为偶发误判而“自毁阈值”，在可靠性与维护成本之间取得平衡。

---

**User**

在光照稳定、摄像头位置及产品位置也固定良好的情况下。依然是 比值阈值的准确率最高嘛？

---

**Cursor**

在“光照稳定 + 相机固定 + 产品位置精准”这种 **理想实验室场景** 下，**绝对阈值法** 与 **颜色比值法** 的优劣会出现一些反转：

1. 绝对阈值法  
   • 环境亮度、白平衡都恒定 ⇒ LED 亮、灭的灰度 / 通道值呈现两团“高斯”分布，且相互间距大。  
   • 只要初始采样得到的阈值落在两团分布之间，中途就几乎不会漂移。  
   • 由于少了一道“除法”运算，数值噪声略低，信噪比(SNR)最高。  
   • 实现简单，计算量最小。

2. 颜色比值法  
   • 把 **色彩纯度** 当指标，本质是  
     R\_ratio = R / (G+B) 或 G\_ratio = G / (R+B)  
   • 当环境稳定时 LED 的比值依旧能很好地区分亮灭，但绝对阈值法已经足够好，比值法不再体现优势。  
   • 反而引入一点额外噪声：分母 (G+B 或 R+B) 里两路通道的随机噪声会扩大方差；如果 LED 发光时略有溢光，周边像素的 R、G、B 都会涨，导致比值波动。  
   • 计算复杂度略高，且需要避免分母过小的异常情况。

3. 综合结论  
   • **光照/位置高度稳定** 时，**绝对阈值法** “极简 + SNR 高” 的优势更明显，准确率往往不逊色甚至略高于比值法。  
   • 比值法真正的价值在于 **抵抗全局亮度或曝光波动**；如果这些因素已经被硬件手段锁定，就属于“备胎”方案。  
   • 实际工程里常见的折中策略：  
     a. 仍然以绝对阈值为主判断；  
     b. 当灰度/通道值落在“模糊带”（阈值 ±Δ）时，再用比值法做第二判据以防万一；  
     c. 或者监测环境亮度的稳定性，一旦波动超过某阈值才自动切换到比值法。

因此，在你描述的稳定场景下 **继续使用（或优化）绝对阈值法即可达到最高准确率**；比值阈值可以作为防御性备份，不必强行替换现有逻辑。

---

**User**

我的使用场景是在室内的工厂、相机位置固定。只是有时候会把工装从左边移动到右边，可能稍微有一些关照亮度的变化。你建议采用哪种方案？

---

**Cursor**

在你的具体场景——  
• 相机永久固定（视场、对焦、白平衡基本不变）  
• 工装偶尔左右移动，导致 **整体或局部光照强弱略有波动**  

我建议采用“**绝对阈值为主 + 亮度系数微调 + 比值兜底**”的组合方案，理由与落地方式如下。  

──────────────────  
1. 绝对阈值仍是主力  
‣ 在大多数帧里，光照只发生<strong>线性强弱变化</strong>，灰度值和通道值依旧保持显著差距，绝对阈值法 SNR 最高、误检最低、计算最省。  

2. 亮度系数 k 做小幅自适应  
‣ 当工装移动到另一边导致画面整体亮 5%–15% 时，只需把所有灰度阈值乘以同样的 k（例如 0.9–1.1 区间），即可保持判定边界。  
‣ k 的计算可以每隔 100 帧对一个“背景参考 ROI”取平均灰度，与采样期均值比值得到。  
‣ 设上下限 ±15%，并设“死区” (|k−1| < 0.03 不调整)，可避免噪声抖动。  

3. 比值阈值作为安全兜底  
‣ 如果某帧里灰度值落在“阈值 ±5” 的模糊带内，再检查  
  • 绿色：G / (R + B) ≥ ratio\_th\_g  
  • 红色：R / (G + B) ≥ ratio\_th\_r  
‣ 只有同时满足才能最终判定为 ON；否则维持 OFF。  
‣ 这样既能抵抗偶发反光，也防止阈值被 k 系数微调后过于接近背景。  

4. 采样流程保持“双阶段离线采样”  
‣ 在换班或维护完成后重新跑一次“全灭–全亮”采样，保证初始阈值可靠。该步骤由人工或上层流程显式触发，不会产生闭环自我污染。  

──────────────────  
落地改动最小化  
• 代码层面只需：  
  ① 在 `led_calculate_thresholds` 之外新增一个 `update_brightness_scale(app_state)`，维护全局 k；  
  ② 在第 274–280 行引用阈值前，将灰度阈值乘以 k；  
  ③ 在“模糊带”情况下插入比值判断，不改变原有逻辑流程。  
• 既保留了你现有 “LED_HISTORY” 去抖、相对亮度抑制等优化，也避免一次性大改算法。  

这样做的结果：  
– 90% 以上的稳定帧仍然走绝对阈值，准确率维持高位；  
– 工装移动导致的 5–15% 亮度波动被 k 吸收；  
– 极端反光或阈值边缘帧由比值法兜底，降低误触发。  

综上，这套“主—辅—兜底”策略能在保持代码结构与性能的前提下，对应你提到的“偶尔亮度变化”风险，达到最优性价比。

---

**User**

那现在其实我不用增加比值、增加K系数也行、或者不增加？ 维持原来的、仅仅优化采样最亮 N % 像素也行？ 你觉得呢

---

**Cursor**

可以，只做“采样最亮 N % 像素”这一项优化就能立刻带来两点好处  
1) 抵抗 ROI 轻微偏移（灯斑仍在框内）  
2) 把背景/杂光对均值的稀释作用降到最低  
而且改动极小：只在取均值前先做一次亮度排序或阈值分段即可，完全符合“最小修改”原则。  

但要不要同时上 k 系数或比值阈值，还取决于下列因素：

1. 亮度波动幅度  
   • 如果工装左右移动后，ROI 内灰度变化＜10 %，多数情况下现有绝对阈值就能容忍，这时只加“最亮 N %”基本够用。  
   • 如果你实测发现灰度能飘到 ±20 % 甚至更多，就建议再加一个 k 系数（实现也不复杂，只是多乘一个比例）。  

2. 误判成本  
   • 允许偶发一次两次漏判/误判 → 可以先不上 k 和比值阈值，观察效果。  
   • 误判一次就要停线 → 建议一步到位至少加 k 这层保险。  

3. 维护便利  
   • “最亮 N %”和 k 系数都属于本地计算，不新增依赖；比值阈值需要多写几行判断，但也不复杂。  
   • 分步骤迭代最安全：  
     第 1 阶段：只改“最亮 N %” → 现场跑几天 → 如果仍有误判，再补 k 或比值。  

4. 代码可扩展性  
   • 现在先实现“最亮 N %”的封装函数，后面想加 k 或比值时，接口基本不用动。  

总结建议  
• 先实现“最亮 N %”采样（N 取 10~15 % 常用），不动其它逻辑，观测生产数据。  
• 如果误判仍偶发且与亮度变化相关，再补一个简单 k 系数；比值阈值可留作备用。  

这样既保持最小改动，又给后续升级留足余地。

---

**User**

好的、目前这个就是最优方案了嘛？ 有没有必要上HSV

---

**Cursor**

是否再“升级”到 HSV，要看目标收益 vs 改动成本：

1. HSV 能带来的优势  
   • Hue（H）基本与亮度解耦：只要饱和度足够，LED 亮暗变化时 H 几乎不变，可用单阈值区分绿色（≈60°）与红色（≈0°/180°）。  
   • Value（V）≈亮度，可独立设置阈值；对曝光变化做 V 比例缩放也直观。  
   • 在 LED 极亮且饱和度高的情况下，H + S 判色通常比 RGB 比值更稳定。

2. 现实中的掣肘  
   • OpenCV 的 `cvtColor` 每帧要多一次矩阵变换；虽然量不大，但比直接用 BGR 略慢。  
   • 红色在 HSV 中跨 0° ↔ 180°，阈值要写成“两段”或 modulo 判断，代码更繁琐。  
   • 当 LED 亮度很低或传感器过曝导致饱和度下降时，H 可能飘动，比值法反而更稳。

3. 与当前“最亮 N % + 灰度（+ k 系数）”方案对比  
   • 你现在的痛点——ROI 轻微偏移 + 亮度小幅波动——通过“最亮 N %”和可选 k 已经基本缓解。  
   • 这些场景下，HSV 提供的额外鲁棒性有限，不能保证“明显更好”；但引入了额外代码路径，需要维护两套阈值。  
   • 若未来出现“背景有同色反射”或“多个颜色干扰”时，再考虑 Hue 过滤更合算。

4. 迭代策略建议  
   a. 先上线“最亮 N %”优化，观察实际误判率。  
   b. 若误判仍主要来自“颜色相近的噪点”，再评估：  
      • 在现有 BGR 基础上增加 Hue 判断（局部改动即可）；  
      • 或彻底切换到 HSV，但那是更大一轮 refactor。

结论  
在你描述的工厂室内环境、已固定相机 + 小幅亮度波动的条件下，“最亮 N % 采样 +（必要时）亮度系数 k”已属于投入产出比最高的方案；HSV 不是必须，现在不改也不会影响最终准确率。把 HSV 当作后备选项，等遇到明显“颜色干扰”再启用，会更符合“最小修改”原则。

---

**User**

好的，请你编写一份完整的优化方案（LED和数码管）的，命名为"第一阶段精度优化V1.md“、实现目前阶段的优化

---

**Cursor**

```markdown
# 第一阶段精度优化 V1

> 适用版本：2025-08-xx 以后所有 **LED / 数码管检测** 模块
> 
> 目标：在尽量 **最小修改** 的前提下，提高 ROI 轻微偏移和室内光照波动场景下的检测稳定性。

---

## 1. 场景与痛点
1. 相机与工装位置固定，但工装在产线左右移动时整体亮度可变化 **±10 %–15 %**。
2. ROI 标注位置存在 1–3 px 偏差；直接对 ROI 全区域取均值时会被背景像素稀释。
3. 现行算法仅依赖 **绝对灰度 / 通道阈值**，在上述情况下偶发漏判或误判。

---

## 2. 优化项总览
| 优化项 | 作用 | 改动成本 | 是否必做 |
|--------|------|----------|---------|
| **A. 采样最亮 N % 像素** | 抵抗 ROI 偏移 & 杂光稀释 | 低 | ✅ 必做 |
| **B. 灰度阈值亮度系数 k** | 抵抗整体亮度 ±15 % 浮动 | 低 | ⚪ 可选（监测后决定） |
| **C. 模糊带颜色比值兜底** | 防止反光导致假亮 | 低-中 | ⚪ 备用 |

> 第一阶段仅实施 **A**；B 与 C 预留代码接口但默认关闭。

---

## 3. 详细设计

### 3.1 采样最亮 N % 像素（核心）
1. **新增常量**：`BRIGHT_PIXEL_PERCENT = 0.10`  <!-- 10 % → 可通过配置文件覆盖 -->
2. **LED 采样 (`led_capture_samples`)**
   - 对单个 ROI：
     ```python
     roi_flat = roi_area.reshape(-1, 3)              # (H*W, 3)
     gray_vals = cv2.cvtColor(roi_area, cv2.COLOR_BGR2GRAY).flatten()
     thresh = np.percentile(gray_vals, 100*(1-BRIGHT_PIXEL_PERCENT))
     mask = gray_vals >= thresh                      # 取最亮 N% 像素
     avg_color = roi_flat[mask].mean(axis=0)
     ```
3. **LED 实时检测 (`detect_led_status`)**
   - 同样在均值计算前加入最亮 N % 过滤；对性能影响 <1 ms/帧（分辨率 640×480, N=10 %）。
4. **数码管检测 (`digit_detector.py`)**
   - 处理逻辑与 LED 相同：在获取 ROI 平均亮度或阈值化前先做最亮 N % 采样。
5. **可配置化**
   - 若 `BRIGHT_PIXEL_PERCENT <= 0` 则退化为原“整 ROI 均值”逻辑。

### 3.2 灰度阈值亮度系数 k（可选）
1. **背景参考 ROI**：在 AppState 增加 `ambient_roi`（位于 LED 板框外黑区域）。
2. **计算方式**：每 100 帧取参考 ROI 灰度均值 `G_now`，与采样阶段均值 `G_ref` 比值 `k = G_now / G_ref`。
3. **应用**：在比较阈值时 `gray_th_eff = gray_th * k`。
4. **安全范围**：`k ∈ [0.85, 1.15]`，变化 <3 % 不生效。

### 3.3 模糊带颜色比值兜底（备用）
1. 若 `(gray_th - 5) < gray_val < (gray_th + 5)`
2. 绿色 LED: `G / (R+B+1e-6) ≥ 1.8`
3. 红色 LED: `R / (G+B+1e-6) ≥ 1.8`

> B、C 在第一阶段**不默认开启**，只提供函数与配置开关。

---

## 4. 代码修改概要
| 文件 | 变更 | 关键行 |
|------|------|-------|
| `constants.py` | 新增 `BRIGHT_PIXEL_PERCENT`、`BRIGHTNESS_K_ENABLE` 等开关 | 末尾 |
| `led_detector.py` | 修改 ROI 取均值逻辑；插入可选 k、自定义比值判断函数 | §261, 274, 279 等 |
| `digit_detector.py` | 同步最亮 N % 采样逻辑 | ROI 均值计算处 |
| `app_state.py` | 新增 `ambient_roi`, `ambient_gray_ref` 等 | 类定义 |

---

## 5. 风险控制 & 回滚
1. **单开关控制**：所有新逻辑受 `ENABLE_PRECISION_OPT_STAGE1` 统一控制，出问题可一键回退。
2. **日志追踪**：打开 DEBUG 时输出 `bright_percentile`, `k 值`, `ratio 判定` 等关键指标。
3. **性能监控**：启动后记录平均帧处理耗时，若增加 >2 ms 即触发告警。

---

## 6. 测试用例
| 类别 | 用例 | 预期 |
|------|------|------|
| ROI 偏移 | ROI 边缘裁 2 px | 误判率≤原先 20 % |
| 亮度变化 | 环灯亮度 ±15 % | 误判率无明显上升 |
| LED 全灭/全亮 | 应全部正确识别 | 100 % 准确 |
| 性能 | 30 fps 输入 | 帧率下降≤1 fps |

---

## 7. 上线步骤
1. 合并代码分支 `feature/precision-opt-v1` ➜ 预发布。  
2. 产线 A/B 测试 3 天，收集日志 → 确认 KPI。  
3. 若通过则开启 `ENABLE_PRECISION_OPT_STAGE1` 开关并部署全线。

---

## 8. 里程碑
| 日期 | 任务 |
|------|------|
| T+0 | 方案评审通过 |
| T+3 | 完成代码开发 & 单元测试 |
| T+5 | 预发布环境集成测试 |
| T+8 | 产线灰度部署 |
| T+11| 全量上线 & 结项 |

---

## 9. 附录
1. **参考文献**：
   - [OpenCV ROI brightness percentile](https://docs.opencv.org/)  
2. **缩写解释**：
   - ROI：Region of Interest  
   - SNR：Signal-to-Noise Ratio  

```

《第一阶段精度优化 V1.md》已创建，包含 LED / 数码管检测的完整改进方案，核心实施项为“采样最亮 N % 像素”，并预留亮度系数 k 与颜色比值兜底的可选开关。欢迎查看并提出调整意见！

---

**User**

很好，那当前的灰度值及通道值哪一种检测的准确率更高、你推荐优先用哪一种还是保持现在的混合OR判断？

---

**Cursor**

先把两条判据各自的“命中特性”拆开来比较，再给出实战推荐。下面默认绿色 LED 用 (G 通道)；红色 LED 用 (R 通道)，灰度用 Gray。

──────────────────────
一、判据对比  
1. 灰度阈值 (Gray ≥ gray_th)  
   • 优点：  
     – 亮灯时三通道同时升高，灰度差距往往最大，**漏检率最低**。  
     – 白平衡轻微飘动对 Gray 几乎无影响。  
   • 缺点：  
     – 强烈背景反光（白光或金属件）也会提高 Gray → **易误判假亮**。  

2. 通道阈值 (G ≥ g_th 或 R ≥ r_th)  
   • 优点：  
     – 对“非本色”的强光反射不敏感，**误判率最低**。  
   • 缺点：  
     – 相机自动白平衡或曝光导致单通道压缩 / 漂移时，易把“真亮”判成“灭” → **漏检风险**。  
     – 当 LED 亮度偏低、饱和度不足时，通道值提升有限。  

3. 混合 OR (Gray ≥ gray_th  **OR**  Color ≥ c_th)  
   • 效果：  
     – 几乎消除漏检（只要 Gray 或本色通道满足其一即可）。  
     – 但在「有色/白色强反光」场景，Gray 触发的 **误判概率最高**。  

──────────────────────
二、如何选择  
1. **产线光源多、金属件反光显著**  
   ‣ 推荐：**以通道阈值为主**，灰度仅在「模糊带」作为兜底（AND 或二级判定），可显著降低误判。  

2. **光源均匀、反光可控，白平衡锁定**  
   ‣ 灰度与通道差别不大；保留 **混合 OR** 方便快速部署，整体准确率最高。  

3. **相机开自动 WB/曝光且环境色温波动明显**  
   ‣ 灰度更稳定，通道易漂移；可 **灰度主判，颜色兜底**。  

──────────────────────
三、推荐实现（与你当前逻辑的差别）  
在保持“最小修改”的前提下，可引入**两级判定**而不是简单 OR：  

```
# ① 主判据：颜色通道
is_on_color = color_val >= color_th

# ② 模糊带兜底：仅当颜色落在 [color_th−Δ, color_th] 时，才检查灰度
if not is_on_color and (color_val >= color_th - COLOR_MARGIN):
    is_on_gray = gray_value >= gray_th
    is_on_final = is_on_gray
else:
    is_on_final = is_on_color
```

• `COLOR_MARGIN` 取 5~10（8bit 图像）即可。  
• 这样让“颜色通道”主导判定，灰度只救回因通道漂移导致的临界漏检。  
• 对反光背景，color_val 往往低于阈值或落在阈值以下较多像素，因此不会触发灰度兜底，误判显著降低。  

──────────────────────
四、如果继续用现有 OR 是否不可？  
并非不可——在光线均匀、工装无强反光的线体，混合 OR 实际误判率低，改动为两级判定带来的收益有限。  
但随着工装/灯罩材质多样化或日后增加高亮指示灯，**两级判定更安全，也便于后续扩展比值法或 HSV**。  

──────────────────────
五、结论  
• 若当前产线误判主要来自“亮金属反光”——**建议切换到“两级判定（颜色主、灰度兜底）”**。  
• 若目前误判很低且想保持零改动——**保留混合 OR** 即可，并先上线“最亮 N % 像素”优化，再按实际数据决定是否升级。

```plaintext
# ① 主判据：颜色通道
is_on_color = color_val >= color_th

# ② 模糊带兜底：仅当颜色落在 [color_th−Δ, color_th] 时，才检查灰度
if not is_on_color and (color_val >= color_th - COLOR_MARGIN):
    is_on_gray = gray_value >= gray_th
    is_on_final = is_on_gray
else:
    is_on_final = is_on_color
```

---

**User**

那其实我如果手动把灰度值的阈值调到大于255，是不是也间接是用通道阈值来做判断了？

---

**Cursor**

是的，从 **最表层的 OR 判定** 来看，  
```
is_on = (gray_value >= gray_th)  OR  (color_val >= color_th)
```  
如果你把 `gray_th` 手动调成 300 (>255)，在 8bit 图像下灰度永远达不到，于是这个表达式就退化为  
```
is_on ≈ (color_val >= color_th)
```  
看似“等价于仅用通道阈值”。

但是请注意两点副作用：  

1. **灰度阈值在其它逻辑里仍被引用**  
   代码中有多处与“最亮 LED 抑制”或“brightness_ratio”相关的判断，比如  
   ```python
   is_brightest_led_truly_bright = (max_gray_value > brightest_gray_th * 1.2)
   ```  
   这里 `brightest_gray_th` 就是前面配置的灰度阈值。如果你把它调成 300，  
   * `is_brightest_led_truly_bright` 几乎永远为 False  
   * 后续针对绿色 LED 的抑制逻辑失效 → 可能出现 **多灯同时被判亮** 的误检  

2. **后续若引入亮度系数 k 或“灰度兜底”时无法再发挥作用**  
   一旦把灰度阈值锁死为 300，就等于硬关闭了灰度链路。将来想启用亮度系数自适应或模糊带灰度兜底，就得先把阈值再改回来，维护成本增加。

――――――――――  
### 更稳妥的做法  
• 保留一个合理的灰度阈值（例如采样期计算出的 80–120），  
• 在 OR 判定前加一层“颜色主、灰度兜底”的逻辑（上条回复中给出的两级判定），  
这样既能避免误触发，又不会破坏其它依赖灰度阈值的分支。  

如果你只是想**快速验证**“通道阈值单独判定”的效果，临时把 `gray_th` 设成 300 没问题；但上线前最好还是用代码层面的开关，而不是把阈值调到无效范围。

```plaintext
is_on = (gray_value >= gray_th)  OR  (color_val >= color_th)
```

```plaintext
is_on ≈ (color_val >= color_th)
```

```python
   is_brightest_led_truly_bright = (max_gray_value > brightest_gray_th * 1.2)
```

