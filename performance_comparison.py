#!/usr/bin/env python3
"""
性能对比测试：同步 vs 异步处理
演示异步处理如何解决UI卡顿问题
"""

import time
import threading
from async_task_manager import AsyncTaskManager, TaskType, TaskStatus

def simulate_ui_loop(duration_seconds=10, fps_target=30):
    """模拟UI主循环"""
    frame_time = 1.0 / fps_target
    frames_rendered = 0
    start_time = time.time()
    
    while time.time() - start_time < duration_seconds:
        frame_start = time.time()
        
        # 模拟UI渲染工作
        time.sleep(0.001)  # 1ms的渲染时间
        frames_rendered += 1
        
        # 等待下一帧
        elapsed = time.time() - frame_start
        if elapsed < frame_time:
            time.sleep(frame_time - elapsed)
    
    actual_duration = time.time() - start_time
    actual_fps = frames_rendered / actual_duration
    return frames_rendered, actual_fps

def test_synchronous_processing():
    """测试同步处理（模拟原始方案）"""
    print("=" * 50)
    print("测试同步处理（原始方案）")
    print("=" * 50)
    
    def blocking_analysis():
        """模拟阻塞的分析操作"""
        print("开始阻塞分析...")
        time.sleep(3)  # 模拟3秒的分析时间
        print("分析完成")
        return {"perfect_cycles": 5, "special_leds_status": "GOOD"}
    
    def blocking_cpu_send():
        """模拟阻塞的CPU通信"""
        print("开始阻塞CPU通信...")
        time.sleep(1)  # 模拟1秒的网络延迟
        print("CPU通信完成")
        return True
    
    # 模拟UI循环，在中间执行阻塞操作
    print("开始UI循环（同步处理）...")
    start_time = time.time()
    frames_before = 0
    frames_after = 0
    
    # 渲染2秒
    print("渲染阶段1（2秒）...")
    frames_before, fps_before = simulate_ui_loop(2)
    
    # 执行阻塞操作
    print("执行阻塞操作...")
    blocking_start = time.time()
    result1 = blocking_analysis()
    result2 = blocking_cpu_send()
    blocking_duration = time.time() - blocking_start
    
    # 继续渲染2秒
    print("渲染阶段2（2秒）...")
    frames_after, fps_after = simulate_ui_loop(2)
    
    total_time = time.time() - start_time
    total_frames = frames_before + frames_after
    average_fps = total_frames / total_time
    
    print(f"\n同步处理结果:")
    print(f"  总时间: {total_time:.2f}秒")
    print(f"  阻塞时间: {blocking_duration:.2f}秒")
    print(f"  阻塞前FPS: {fps_before:.1f}")
    print(f"  阻塞后FPS: {fps_after:.1f}")
    print(f"  平均FPS: {average_fps:.1f}")
    print(f"  UI卡顿时间: {blocking_duration:.2f}秒")
    
    return {
        'total_time': total_time,
        'blocking_time': blocking_duration,
        'average_fps': average_fps,
        'ui_freeze_time': blocking_duration
    }

def test_asynchronous_processing():
    """测试异步处理（新方案）"""
    print("\n" + "=" * 50)
    print("测试异步处理（新方案）")
    print("=" * 50)
    
    # 启动异步任务管理器
    manager = AsyncTaskManager()
    manager.start()
    
    try:
        print("开始UI循环（异步处理）...")
        start_time = time.time()
        total_frames = 0
        analysis_task_id = None
        cpu_task_id = None
        analysis_result = None
        
        # 模拟UI主循环
        target_duration = 8  # 8秒测试
        frame_time = 1.0 / 30  # 30 FPS
        
        while time.time() - start_time < target_duration:
            frame_start = time.time()
            
            # 模拟UI渲染
            time.sleep(0.001)  # 1ms渲染时间
            total_frames += 1
            
            # 在第2秒时提交分析任务
            if time.time() - start_time > 2 and analysis_task_id is None:
                print("提交异步分析任务...")
                analysis_task_id = manager.submit_task(
                    TaskType.ANALYZE_LOG,
                    {'log_file_path': 'test_log.txt'}
                )
            
            # 检查分析任务状态
            if analysis_task_id and manager.get_task_status(analysis_task_id) == TaskStatus.COMPLETED:
                if analysis_result is None:
                    analysis_result = manager.get_task_result(analysis_task_id)
                    print("分析任务完成，提交CPU通信任务...")
                    cpu_task_id = manager.submit_task(
                        TaskType.SEND_CPU_SIGNAL,
                        {'value': 1, 'address': 10}
                    )
            
            # 检查CPU任务状态
            if cpu_task_id and manager.get_task_status(cpu_task_id) == TaskStatus.COMPLETED:
                print("CPU通信任务完成")
                cpu_task_id = None  # 标记完成
            
            # 等待下一帧
            elapsed = time.time() - frame_start
            if elapsed < frame_time:
                time.sleep(frame_time - elapsed)
        
        total_time = time.time() - start_time
        average_fps = total_frames / total_time
        
        print(f"\n异步处理结果:")
        print(f"  总时间: {total_time:.2f}秒")
        print(f"  总帧数: {total_frames}")
        print(f"  平均FPS: {average_fps:.1f}")
        print(f"  UI卡顿时间: 0.00秒")
        
        return {
            'total_time': total_time,
            'blocking_time': 0,
            'average_fps': average_fps,
            'ui_freeze_time': 0
        }
        
    finally:
        manager.stop()

def main():
    """主函数"""
    print("UI性能对比测试")
    print("模拟接收88信号后的数据分析和CPU通信过程")
    
    # 创建测试日志文件
    with open('test_log.txt', 'w', encoding='utf-8') as f:
        f.write("2024-01-01 12:00:00.000 - INFO - LED G1: Status=ON\n")
    
    try:
        # 测试同步处理
        sync_results = test_synchronous_processing()
        
        # 测试异步处理
        async_results = test_asynchronous_processing()
        
        # 对比结果
        print("\n" + "=" * 50)
        print("性能对比结果")
        print("=" * 50)
        
        print(f"同步处理:")
        print(f"  平均FPS: {sync_results['average_fps']:.1f}")
        print(f"  UI卡顿时间: {sync_results['ui_freeze_time']:.2f}秒")
        
        print(f"\n异步处理:")
        print(f"  平均FPS: {async_results['average_fps']:.1f}")
        print(f"  UI卡顿时间: {async_results['ui_freeze_time']:.2f}秒")
        
        fps_improvement = async_results['average_fps'] - sync_results['average_fps']
        freeze_reduction = sync_results['ui_freeze_time'] - async_results['ui_freeze_time']
        
        print(f"\n改进效果:")
        print(f"  FPS提升: +{fps_improvement:.1f}")
        print(f"  卡顿减少: -{freeze_reduction:.2f}秒")
        print(f"  卡顿减少百分比: {(freeze_reduction/sync_results['ui_freeze_time']*100):.1f}%")
        
        print(f"\n✓ 异步处理成功解决了UI卡顿问题！")
        
    finally:
        # 清理测试文件
        import os
        if os.path.exists('test_log.txt'):
            os.remove('test_log.txt')

if __name__ == "__main__":
    main()
