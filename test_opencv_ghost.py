"""
测试OpenCV幽灵窗口实现
"""
import sys
import time
import cv2
import numpy as np

# 添加项目路径
sys.path.append('.')
from tk_ghost_window import TkGhostWindow

def test_opencv_ghost_window():
    """测试OpenCV幽灵窗口"""
    print("=== 测试OpenCV幽灵窗口 ===")
    
    # 创建主窗口
    cv2.namedWindow("Main Test", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Main Test", 640, 480)
    
    # 创建OpenCV幽灵窗口
    ghost_win = TkGhostWindow("TestGhost")
    ghost_win.start()
    
    try:
        print("测试开始，按 ESC 键退出...")
        print("2秒后应该会出现一个1x1像素的小窗口")
        
        frame_count = 0
        start_time = time.time()
        
        while True:
            # 创建一个简单的测试帧
            test_frame = np.full((480, 640, 3), (64, 64, 64), dtype=np.uint8)
            
            # 添加一些动态内容
            x = int(320 + 200 * np.sin(frame_count * 0.05))
            y = int(240 + 200 * np.cos(frame_count * 0.05))
            cv2.circle(test_frame, (x, y), 20, (0, 255, 0), -1)
            
            # 显示主窗口
            cv2.imshow("Main Test", test_frame)
            
            # 处理按键
            key = cv2.waitKey(30) & 0xFF
            if key == 27:  # ESC
                break
            
            frame_count += 1
            
            # 每3秒改变一次幽灵窗口颜色
            if frame_count % 100 == 0:
                colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255)]
                color = colors[frame_count // 100 % len(colors)]
                ghost_win.set_color(color)
                print(f"幽灵窗口颜色变更为: {color}")
            
            # 每5秒移动一次幽灵窗口位置
            if frame_count % 166 == 0:
                positions = [(50, 50), (100, 100), (200, 200), (300, 300), (400, 400)]
                pos = positions[frame_count // 166 % len(positions)]
                ghost_win.set_position(pos[0], pos[1])
                print(f"幽灵窗口位置变更为: {pos}")
            
            # 每秒输出一次状态
            if frame_count % 33 == 0:
                fps = frame_count / (time.time() - start_time)
                print(f"主UI运行正常，FPS: {fps:.1f}")
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    
    finally:
        ghost_win.stop()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    test_opencv_ghost_window()
