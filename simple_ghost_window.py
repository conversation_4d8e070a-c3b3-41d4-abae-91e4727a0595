"""
简化版幽灵窗口 - 使用PyQt5创建，避免与OpenCV冲突
"""
import sys
import time
import logging
import threading
from PyQt5.QtWidgets import QApplication, QWidget, QLabel
from PyQt5.QtCore import Qt, QTimer, QPoint
from PyQt5.QtGui import QPalette, QColor

class SimpleGhostWindow:
    """简化的幽灵窗口实现"""
    
    def __init__(self, window_title="GhostWindow"):
        self.window_title = window_title
        self.is_running = False
        self.thread = None
        self.app = None
        self.window = None
        
        # 窗口设置
        self.window_size = 2
        self.window_x = 50
        self.window_y = 50
        self.window_color = (128, 128, 128)
        
    def create_window(self):
        """创建幽灵窗口"""
        try:
            # 创建QApplication实例（如果不存在）
            if not QApplication.instance():
                self.app = QApplication(sys.argv)
            
            # 创建窗口
            self.window = QWidget()
            self.window.setWindowTitle(self.window_title)
            self.window.setFixedSize(self.window_size, self.window_size)
            
            # 设置窗口标志
            self.window.setWindowFlags(
                Qt.WindowStaysOnTopHint |  # 窗口置顶
                Qt.Tool |                  # 工具窗口（不在任务栏显示）
                Qt.FramelessWindowHint     # 无边框
            )
            
            # 设置窗口背景色
            palette = self.window.palette()
            palette.setColor(QPalette.Background, QColor(*self.window_color))
            self.window.setPalette(palette)
            
            # 移动窗口到指定位置
            self.window.move(self.window_x, self.window_y)
            
            # 显示窗口
            self.window.show()
            
            logging.info(f"简化幽灵窗口 '{self.window_title}' 创建成功")
            
        except Exception as e:
            logging.error(f"创建简化幽灵窗口失败: {e}")
            self.window = None
    
    def update_window(self):
        """更新窗口"""
        if self.window is None:
            return
            
        try:
            # 更新窗口颜色
            palette = self.window.palette()
            palette.setColor(QPalette.Background, QColor(*self.window_color))
            self.window.setPalette(palette)
            
            # 更新窗口位置
            self.window.move(self.window_x, self.window_y)
            
        except Exception as e:
            logging.error(f"更新简化幽灵窗口失败: {e}")
    
    def _run_loop(self):
        """幽灵窗口运行循环"""
        logging.info("简化幽灵窗口线程启动")
        
        # 等待延迟时间后再创建窗口
        time.sleep(2.0)  # 增加延迟确保主窗口先创建
        
        # 创建窗口
        self.create_window()
        
        if self.window is None:
            logging.error("无法创建简化幽灵窗口")
            return
        
        # 创建定时器更新窗口
        timer = QTimer()
        timer.timeout.connect(self.update_window)
        timer.start(1000)  # 每秒更新一次
        
        # 运行Qt事件循环
        if self.app:
            self.app.exec_()
        
        logging.info("简化幽灵窗口线程停止")
    
    def start(self):
        """启动幽灵窗口"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # 启动线程
        self.thread = threading.Thread(target=self._run_loop, daemon=True)
        self.thread.start()
        
        logging.info("简化幽灵窗口已启动")
    
    def stop(self):
        """停止幽灵窗口"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 关闭窗口
        if self.window:
            try:
                self.window.close()
            except Exception as e:
                logging.error(f"关闭简化幽灵窗口失败: {e}")
        
        # 退出应用
        if self.app:
            try:
                self.app.quit()
            except Exception as e:
                logging.error(f"退出Qt应用失败: {e}")
        
        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)
        
        self.window = None
        self.app = None
        logging.info("简化幽灵窗口已停止")
    
    def set_position(self, x, y):
        """设置窗口位置"""
        self.window_x = x
        self.window_y = y
        if self.window:
            try:
                self.window.move(x, y)
            except Exception as e:
                logging.error(f"移动简化幽灵窗口失败: {e}")
    
    def set_color(self, rgb_color):
        """设置窗口颜色"""
        self.window_color = rgb_color
        if self.window:
            try:
                palette = self.window.palette()
                palette.setColor(QPalette.Background, QColor(*rgb_color))
                self.window.setPalette(palette)
            except Exception as e:
                logging.error(f"设置简化幽灵窗口颜色失败: {e}")

# 全局简化幽灵窗口实例
simple_ghost_window = None

def initialize_simple_ghost_window():
    """初始化全局简化幽灵窗口"""
    global simple_ghost_window
    if simple_ghost_window is None:
        simple_ghost_window = SimpleGhostWindow()
        simple_ghost_window.start()
        logging.info("全局简化幽灵窗口已初始化")
    return simple_ghost_window

def shutdown_simple_ghost_window():
    """关闭全局简化幽灵窗口"""
    global simple_ghost_window
    if simple_ghost_window is not None:
        simple_ghost_window.stop()
        simple_ghost_window = None
        logging.info("全局简化幽灵窗口已关闭")

def get_simple_ghost_window():
    """获取全局简化幽灵窗口实例"""
    global simple_ghost_window
    return simple_ghost_window