#!/usr/bin/env python3
"""
LED详细状态推送功能测试脚本
测试LED状态是否能正确推送到前端弹窗
"""

import sys
import os
import time
import threading
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simple_websocket_server import simple_websocket_server

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def create_test_led_log():
    """创建测试LED日志文件"""
    log_content = """2025-08-24 14:30:15.123 - INFO - LED G1: Status=ON, Gray=45.2, Green=78.5, Red=12.3, Brightness=75.5%
2025-08-24 14:30:15.124 - INFO - LED G2: Status=OFF, Gray=12.1, Green=8.2, Red=5.1, Brightness=0.0%
2025-08-24 14:30:15.125 - INFO - LED G3: Status=ON, Gray=52.3, Green=82.1, Red=15.2, Brightness=80.2%
2025-08-24 14:30:15.126 - INFO - LED G4: Status=OFF, Gray=8.5, Green=5.2, Red=3.1, Brightness=0.0%
2025-08-24 14:30:15.127 - INFO - LED G5: Status=ON, Gray=48.7, Green=75.3, Red=18.9, Brightness=72.1%
2025-08-24 14:30:15.128 - INFO - LED G6: Status=OFF, Gray=15.2, Green=12.1, Red=8.5, Brightness=0.0%
2025-08-24 14:30:15.129 - INFO - LED G7: Status=ON, Gray=55.8, Green=88.2, Red=22.1, Brightness=85.3%
2025-08-24 14:30:15.130 - INFO - LED G8: Status=OFF, Gray=9.8, Green=6.5, Red=4.2, Brightness=0.0%
2025-08-24 14:30:15.131 - INFO - LED G9: Status=ON, Gray=42.1, Green=68.9, Red=16.5, Brightness=68.9%
2025-08-24 14:30:15.132 - INFO - LED G10: Status=OFF, Gray=11.2, Green=8.9, Red=5.8, Brightness=0.0%
2025-08-24 14:30:15.133 - INFO - LED G11: Status=ON, Gray=49.5, Green=79.2, Red=19.8, Brightness=76.5%
2025-08-24 14:30:15.134 - INFO - LED G12: Status=OFF, Gray=13.8, Green=10.2, Red=7.1, Brightness=0.0%
2025-08-24 14:30:15.135 - INFO - LED G13: Status=ON, Gray=51.2, Green=81.5, Red=21.2, Brightness=78.9%
2025-08-24 14:30:15.136 - INFO - LED G14: Status=OFF, Gray=10.5, Green=7.8, Red=5.2, Brightness=0.0%
2025-08-24 14:30:15.137 - INFO - LED G15: Status=ON, Gray=46.8, Green=74.2, Red=17.5, Brightness=71.8%
2025-08-24 14:30:15.138 - INFO - LED G16: Status=OFF, Gray=12.8, Green=9.5, Red=6.2, Brightness=0.0%
2025-08-24 14:30:15.139 - INFO - LED G17: Status=ON, Gray=53.1, Green=84.8, Red=23.5, Brightness=82.1%
2025-08-24 14:30:15.140 - INFO - LED G18: Status=OFF, Gray=14.2, Green=11.8, Red=8.1, Brightness=0.0%
2025-08-24 14:30:15.141 - INFO - LED G19: Status=ON, Gray=44.5, Green=71.2, Red=15.8, Brightness=69.5%
2025-08-24 14:30:15.142 - INFO - LED G20: Status=OFF, Gray=9.2, Green=6.8, Red=4.5, Brightness=0.0%
2025-08-24 14:30:15.143 - INFO - LED G21: Status=ON, Gray=50.8, Green=80.5, Red=20.2, Brightness=77.8%
2025-08-24 14:30:15.144 - INFO - LED G22: Status=OFF, Gray=11.8, Green=8.5, Red=5.8, Brightness=0.0%
2025-08-24 14:30:15.145 - INFO - LED G23: Status=ON, Gray=47.2, Green=76.8, Red=18.2, Brightness=73.5%
2025-08-24 14:30:15.146 - INFO - LED G24: Status=OFF, Gray=13.2, Green=10.8, Red=7.5, Brightness=0.0%
2025-08-24 14:30:15.147 - INFO - LED G25: Status=ON, Gray=52.8, Green=83.2, Red=22.8, Brightness=81.2%
2025-08-24 14:30:15.148 - INFO - LED G26: Status=OFF, Gray=10.8, Green=7.2, Red=4.8, Brightness=0.0%
2025-08-24 14:30:15.149 - INFO - LED G27: Status=ON, Gray=45.8, Green=72.5, Red=16.8, Brightness=70.2%
2025-08-24 14:30:15.150 - INFO - LED G28: Status=OFF, Gray=12.5, Green=9.8, Red=6.5, Brightness=0.0%
2025-08-24 14:30:15.151 - INFO - LED G29: Status=ON, Gray=49.8, Green=78.8, Red=19.5, Brightness=76.2%
2025-08-24 14:30:15.152 - INFO - LED G30: Status=OFF, Gray=11.5, Green=8.2, Red=5.5, Brightness=0.0%
2025-08-24 14:30:15.153 - INFO - LED G31: Status=ON, Gray=54.2, Green=86.5, Red=24.2, Brightness=84.5%
2025-08-24 14:30:15.154 - INFO - LED G32: Status=OFF, Gray=9.5, Green=6.2, Red=4.1, Brightness=0.0%
2025-08-24 14:30:15.155 - INFO - LED G33: Status=ON, Gray=43.8, Green=69.5, Red=15.2, Brightness=67.8%
2025-08-24 14:30:15.156 - INFO - LED R1: Status=ON, Gray=58.2, Green=15.8, Red=92.5, Brightness=88.2%
2025-08-24 14:30:15.157 - INFO - LED R2: Status=OFF, Gray=8.2, Green=3.5, Red=12.8, Brightness=0.0%"""
    
    log_file_path = os.path.join(os.path.dirname(__file__), "led_digit_detection.log")
    with open(log_file_path, 'w', encoding='utf-8') as f:
        f.write(log_content)
    
    return log_file_path

def simulate_led_changes(log_file_path):
    """模拟LED状态变化"""
    changes = [
        "2025-08-24 14:30:16.200 - INFO - LED G1 state ON->OFF",
        "2025-08-24 14:30:16.201 - INFO - LED G1: Status=OFF, Gray=8.5, Green=5.2, Red=3.1, Brightness=0.0%",
        "2025-08-24 14:30:17.300 - INFO - LED G2 state OFF->ON", 
        "2025-08-24 14:30:17.301 - INFO - LED G2: Status=ON, Gray=48.5, Green=76.2, Red=18.5, Brightness=74.2%",
        "2025-08-24 14:30:18.400 - INFO - LED R1 state ON->OFF",
        "2025-08-24 14:30:18.401 - INFO - LED R1: Status=OFF, Gray=12.5, Green=8.2, Red=15.8, Brightness=0.0%",
        "2025-08-24 14:30:19.500 - INFO - LED R2 state OFF->ON",
        "2025-08-24 14:30:19.501 - INFO - LED R2: Status=ON, Gray=55.8, Green=12.5, Red=88.2, Brightness=85.5%",
    ]
    
    for change in changes:
        time.sleep(1)
        with open(log_file_path, 'a', encoding='utf-8') as f:
            f.write('\n' + change)
        print(f"添加变化: {change.split(' - ')[-1]}")

def test_led_detailed_status():
    """测试LED详细状态推送功能"""
    print("=" * 60)
    print("LED详细状态推送功能测试")
    print("=" * 60)
    
    # 创建测试LED日志文件
    print("1. 创建测试LED日志文件...")
    log_file_path = create_test_led_log()
    print(f"   ✅ 测试日志文件已创建: {log_file_path}")
    
    # 启动WebSocket服务器
    print("\n2. 启动WebSocket服务器...")
    try:
        server_thread = threading.Thread(target=simple_websocket_server.start_server, daemon=True)
        server_thread.start()
        time.sleep(2)  # 等待服务器启动
        
        if simple_websocket_server.running:
            print("   ✅ WebSocket服务器启动成功")
        else:
            print("   ❌ WebSocket服务器启动失败")
            return False
            
    except Exception as e:
        print(f"   ❌ WebSocket服务器启动异常: {e}")
        return False
    
    # 启动LED监控
    print("\n3. 启动LED日志监控...")
    try:
        simple_websocket_server.start_led_monitoring(log_file_path)
        print("   ✅ LED日志监控已启动")
        print("   💡 应该推送了初始的LED状态到前端")
    except Exception as e:
        print(f"   ❌ LED日志监控启动失败: {e}")
        return False
    
    # 模拟检测开始
    print("\n4. 模拟检测开始...")
    try:
        simple_websocket_server.start_detection(
            duration=30,
            message="测试LED详细状态推送..."
        )
        print("   ✅ 检测已开始，应该显示LED状态弹窗")
    except Exception as e:
        print(f"   ❌ 启动检测失败: {e}")
        return False
    
    # 等待一段时间让用户观察初始状态
    print("\n5. 等待5秒，请在网页端查看LED状态弹窗...")
    for i in range(5, 0, -1):
        print(f"   等待 {i} 秒...", end='\r')
        time.sleep(1)
    print("   等待完成")
    
    # 模拟LED状态变化
    print("\n6. 模拟LED状态变化...")
    change_thread = threading.Thread(target=simulate_led_changes, args=(log_file_path,), daemon=True)
    change_thread.start()
    
    # 等待变化完成
    print("   💡 正在模拟LED状态变化，请观察网页端弹窗中的LED状态更新...")
    change_thread.join()
    
    # 继续运行一段时间
    print("\n7. 继续运行15秒，请验证LED状态更新...")
    for remaining in range(15, 0, -1):
        print(f"   剩余时间: {remaining}秒", end='\r')
        time.sleep(1)
    
    print("\n\n8. 停止检测...")
    try:
        simple_websocket_server.complete_detection("测试完成")
        print("   ✅ 检测已完成，LED状态弹窗应该自动关闭")
    except Exception as e:
        print(f"   ⚠️  停止检测时出现异常: {e}")
    
    print("\n9. 停止WebSocket服务器...")
    try:
        simple_websocket_server.stop_server()
        print("   ✅ WebSocket服务器已停止")
    except Exception as e:
        print(f"   ⚠️  停止服务器时出现异常: {e}")
    
    # 清理测试文件
    try:
        os.remove(log_file_path)
        print("   ✅ 测试文件已清理")
    except Exception as e:
        print(f"   ⚠️  清理测试文件失败: {e}")
    
    print("\n测试完成！")
    return True

if __name__ == "__main__":
    try:
        # 运行测试
        success = test_led_detailed_status()
        
        if success:
            print("\n🎉 LED详细状态推送测试完成！")
            print("💡 如果网页端LED状态弹窗正确显示和更新，说明功能正常工作")
            print("\n预期效果:")
            print("1. 检测开始时自动显示LED状态弹窗")
            print("2. 弹窗中显示33个绿色LED和2个红色LED的状态")
            print("3. LED状态变化时，对应的LED会闪烁并更新状态")
            print("4. 检测完成时弹窗自动关闭")
        else:
            print("\n❌ 测试失败，请检查配置和日志")
            
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        try:
            simple_websocket_server.stop_server()
        except:
            pass
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
