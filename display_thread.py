"""
显示线程模块 - 专门处理UI显示，与采集/处理线程解耦
"""
import cv2
import threading
import queue
import time
import logging
from constants import MAIN_WINDOW


class DisplayWorker:
    """显示工作线程，负责UI显示和用户交互"""
    
    def __init__(self, display_queue, shared_state):
        """
        初始化显示工作线程
        
        Args:
            display_queue: 显示帧队列 (maxsize=1)
            shared_state: 线程间共享状态对象
        """
        self.display_queue = display_queue
        self.shared_state = shared_state
        self.thread = None
        self.running = False
        
        # 显示配置
        self.display_size = (960, 540)  # 固定显示尺寸
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.display_fps = 0.0
        
    def start(self):
        """启动显示线程"""
        if self.thread is not None and self.thread.is_alive():
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._display_loop, daemon=True)
        self.thread.start()
        logging.info("显示线程已启动")
        
    def stop(self):
        """停止显示线程"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        logging.info("显示线程已停止")
        
    def _display_loop(self):
        """显示线程主循环"""
        logging.info("显示线程开始运行")
        try:
            while self.running:
                try:
                    # 从队列获取最新帧，超时避免阻塞
                    display_frame = self.display_queue.get(timeout=0.1)

                    if display_frame is not None:
                        # 缩放到固定显示尺寸
                        small_frame = cv2.resize(
                            display_frame,
                            self.display_size,
                            interpolation=cv2.INTER_AREA
                        )

                        # 显示帧
                        cv2.imshow(MAIN_WINDOW, small_frame)

                        # 更新显示FPS
                        self._update_display_fps()

                    # 处理按键事件
                    key = cv2.waitKey(1) & 0xFF
                    if key != 255:  # 有按键按下
                        self.shared_state.set_last_key(key)
                        if key == ord('q'):
                            self.shared_state.request_exit()

                except queue.Empty:
                    # 队列为空，继续循环
                    # 仍需要处理按键事件
                    key = cv2.waitKey(1) & 0xFF
                    if key != 255:
                        self.shared_state.set_last_key(key)
                        if key == ord('q'):
                            self.shared_state.request_exit()
                    continue
                except Exception as e:
                    logging.error(f"显示线程异常: {e}")
                    continue

        except Exception as e:
            logging.error(f"显示线程严重错误: {e}")
        finally:
            logging.info("显示线程退出")
            
    def _update_display_fps(self):
        """更新显示FPS统计"""
        self.fps_counter += 1
        current_time = time.time()
        
        if current_time - self.fps_start_time >= 1.0:
            self.display_fps = self.fps_counter / (current_time - self.fps_start_time)
            self.shared_state.display_fps = self.display_fps
            self.fps_counter = 0
            self.fps_start_time = current_time


class SharedState:
    """线程间共享状态"""
    
    def __init__(self):
        self.should_exit = False
        self.last_key = 255  # 最后按下的键
        self.display_fps = 0.0
        self.processing_fps = 0.0
        
        # 线程安全锁（如果需要的话）
        self.lock = threading.Lock()
        
    def get_and_clear_key(self):
        """获取并清除最后按下的键"""
        with self.lock:
            key = self.last_key
            self.last_key = 255
            return key

    def set_last_key(self, key: int):
        with self.lock:
            self.last_key = key

    def request_exit(self):
        with self.lock:
            self.should_exit = True

    def set_processing_fps(self, fps):
        """设置处理FPS"""
        with self.lock:
            self.processing_fps = fps
