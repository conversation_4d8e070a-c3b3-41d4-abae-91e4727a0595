import cv2
import time
import logging
import psutil

from app_state import AppState, app_state
from constants import *
import config_manager
import camera_manager
import ui_handler # Import the UI handler
import async_task_manager # <--- 导入异步任务管理器
from simple_websocket_server import simple_websocket_server
from display_thread import DisplayWorker, SharedState
from processing_thread import ProcessingWorker
from capture_thread import CaptureWorker
from tk_ghost_window import initialize_tk_ghost_window, shutdown_tk_ghost_window



# 在部分平台（尤其 Windows）上，OpenCV GUI 需要在主线程调用
import high_res_timer
high_res_timer.enable_1ms()

# 为避免黑屏，这里提供一个开关：False 时在主线程显示
USE_DISPLAY_THREAD = False

# --- 日志设置 --- Constants
# LOG_FILE = 'led_digit_detection.log' # <--- 移除这里的定义
LOG_LEVEL = logging.INFO  # 改回INFO，以提高生产环境稳定性
# 使用旧版严格格式：时间戳.毫秒 - 级别 - 消息（不包含logger名称），以匹配分析脚本正则
LOG_FORMAT = '%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

def setup_logging(app_state: AppState):
    """配置日志记录，使用 FileHandler 并将其存储在 AppState 中。"""
    # 获取根 logger
    logger = logging.getLogger()
    logger.setLevel(LOG_LEVEL)

    # --- 尝试移除根 logger 可能存在的旧 handlers --- #
    # 这有助于避免由默认配置或第三方库添加的不期望的控制台输出
    # 注意: 如果其他库确实需要它们自己的 handler，这可能会产生副作用
    if logger.hasHandlers():
        print(f"Root logger has handlers: {logger.handlers}, attempting to remove them.")
        logging.warning(f"Root logger has handlers: {logger.handlers}, attempting to remove them.")
        for handler in logger.handlers[:]: # Iterate over a copy
            try:
                # handler.close() # 关闭 handler
                logger.removeHandler(handler)
            except Exception as e:
                print(f"Error removing handler {handler}: {e}")
                logging.error(f"Error removing handler {handler}: {e}")
    # ------------------------------------------------- #

    # 创建 FileHandler
    # 使用 mode='w' 确保每次启动时清空旧日志（如果这是期望行为）
    # 如果希望追加日志，请使用 mode='a'
    # 根据需求，这里初始时不清空，由程序逻辑控制清空
    try:
        from utils.paths import get_log_path
        file_handler = logging.FileHandler(str(get_log_path(LOG_FILE)), mode='a', encoding='utf-8')
    except OSError as e:
        print(f"错误：无法打开日志文件 {LOG_FILE} 进行写入: {e}")
        # 可以选择记录到控制台或退出
        logging.basicConfig(level=LOG_LEVEL, format=LOG_FORMAT, datefmt=LOG_DATE_FORMAT)
        logging.error(f"无法初始化文件日志处理器: {e}")
        app_state.log_file_handler = None # 标记 handler 创建失败
        return

    # 创建并设置 Formatter
    formatter = logging.Formatter(LOG_FORMAT, datefmt=LOG_DATE_FORMAT)
    file_handler.setFormatter(formatter)

    # 将 handler 存储在 AppState 中，以便动态添加/移除
    app_state.log_file_handler = file_handler

    # 初始时不将 handler 添加到 logger，由状态机控制
    # logger.addHandler(file_handler)

    # 添加 StreamHandler 以同时输出到控制台
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

    #print(f"日志系统初始化完成。日志将根据程序逻辑动态写入到: {LOG_FILE}")
    logging.info("程序启动 - 日志系统初始化") # 这条会被记录，因为 logger level 已设置

def main():
    print("程序启动中...")

    # 1. 使用全局 AppState 实例
    # app_state = AppState()  # 不再创建新实例，使用全局实例
    app_state.prev_time = time.time() # 初始化 FPS 计时器

    # 2. 配置日志系统
    setup_logging(app_state)
    print("日志系统初始化完成")
    # --- 日志设置结束 ---

    # 3. 初始化异步任务管理器
    async_task_manager.initialize_task_manager()
    logging.info("异步任务管理器已启动")

    # 4. 启动WebSocket服务器
    simple_websocket_server.start_server()
    logging.info("WebSocket服务器已启动")

    # 5. 加载配置
    config_manager.load_config(app_state) # 会根据配置文件或用户输入设置初始模式

    # 6. 初始化摄像头
    if not camera_manager.initialize_camera(app_state):
        logging.error("摄像头初始化失败，程序退出。")
        print("摄像头初始化失败，程序退出。")
        # 关闭WebSocket服务器
        simple_websocket_server.stop_server()
        # 关闭异步任务管理器
        async_task_manager.shutdown_task_manager()
        return # 无法继续

    # 7. 启动Tkinter幽灵窗口
    try:
        initialize_tk_ghost_window()
        logging.info("Tkinter幽灵窗口已启动")
        print("Tkinter幽灵窗口已启动（2秒后创建）")
    except Exception as e:
        logging.warning(f"启动Tkinter幽灵窗口失败: {e}")
        print(f"启动Tkinter幽灵窗口失败: {e}")

    # 8. 提升主线程优先级
    try:
        current_process = psutil.Process()
        current_process.nice(psutil.HIGH_PRIORITY_CLASS)
        logging.info("主线程优先级已提升")
        print("主线程优先级已提升")
    except Exception as e:
        logging.warning(f"无法提升进程优先级: {e}")
        print(f"无法提升进程优先级: {e}")

    # 9. 初始化线程通信
    print("初始化线程通信...")
    app_state.shared_state = SharedState()
    app_state.display_worker = DisplayWorker(app_state.display_queue, app_state.shared_state)
    app_state.processing_worker = ProcessingWorker(app_state)
    app_state.capture_worker = CaptureWorker(app_state)

    # 设置 ui_handler 的共享状态
    ui_handler.set_shared_state(app_state.shared_state)
    print("线程通信初始化完成")

    # 10. 创建窗口并设置鼠标回调
    print("创建窗口...")
    cv2.namedWindow(MAIN_WINDOW, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(MAIN_WINDOW, 960, 540)
    ui_handler.setup_mouse_callback(MAIN_WINDOW, app_state)

    # 11. 启动工作线程
    if USE_DISPLAY_THREAD:
        # 启动显示线程（通常禁用）
        print("启动显示线程...")
        app_state.display_worker.start()
    
    # 启动采集线程（三线程架构）
    try:
        print("启动采集线程...")
        app_state.capture_worker.start()
    except Exception as e:
        logging.error(f"启动采集线程失败: {e}")

    # 启动处理线程
    print("启动处理线程...")
    app_state.processing_worker.start()

    # 12. 主循环（GUI + 按键处理）
    try:
        def is_window_visible(win_name: str) -> bool:
            try:
                v = cv2.getWindowProperty(win_name, cv2.WND_PROP_VISIBLE)
                if v <= 0:
                    return False
                # 如果支持，进一步检查窗口大小（最小化时可能为0）
                if hasattr(cv2, 'getWindowImageRect'):
                    _, _, w, h = cv2.getWindowImageRect(win_name)
                    if w <= 0 or h <= 0:
                        return False
                return True
            except Exception:
                # 无法获取属性时，假定可见，避免误杀刷新
                return True

        while app_state.running and not app_state.shared_state.should_exit:
            # 从处理线程队列读取最新帧；尝试丢旧留新
            frame = None
            try:
                frame = app_state.display_queue.get(timeout=0.01)
                while True:
                    frame = app_state.display_queue.get_nowait()
            except Exception:
                pass

            visible = is_window_visible(MAIN_WINDOW)
            if frame is not None and visible:
                cv2.imshow(MAIN_WINDOW, frame)

            # 主线程处理按键：统一使用 waitKey(1)，避免 busy loop 抢占 GIL
            key = cv2.waitKey(1) & 0xFF
            
            # 简化幽灵窗口不需要在主循环中更新

            # 在窗口不可见/最小化时，轻微让出 CPU，减少主线程忙轮询
            if not visible:
                time.sleep(0.001)

            if key != -1 and key != 255:
                app_state.shared_state.set_last_key(key)
                if key == ord('q'):
                    app_state.shared_state.request_exit()

            # 检查退出
            if app_state.shared_state.should_exit:
                logging.info("用户请求退出。")
                app_state.running = False

            # 可选：每次循环小憩（不阻塞）
            # time.sleep(0)

    except KeyboardInterrupt:
        logging.info("收到键盘中断信号")
        app_state.running = False


    # 13. 清理资源
    logging.info("正在关闭程序...")
    print("正在关闭程序...")

    # 停止处理/显示线程
    if hasattr(app_state, 'processing_worker') and app_state.processing_worker:
        app_state.processing_worker.stop()
    if app_state.display_worker and USE_DISPLAY_THREAD:
        app_state.display_worker.stop()
    if hasattr(app_state, 'capture_worker') and app_state.capture_worker:
        app_state.capture_worker.stop()

    # 关闭Tkinter幽灵窗口
    try:
        shutdown_tk_ghost_window()
        logging.info("Tkinter幽灵窗口已关闭")
        print("Tkinter幽灵窗口已关闭")
    except Exception as e:
        logging.warning(f"关闭Tkinter幽灵窗口失败: {e}")
        print(f"关闭Tkinter幽灵窗口失败: {e}")

    # 关闭WebSocket服务器
    simple_websocket_server.stop_server()
    logging.info("WebSocket服务器已关闭")

    # 关闭异步任务管理器
    async_task_manager.shutdown_task_manager()
    logging.info("异步任务管理器已关闭")

    camera_manager.release_camera(app_state) # 释放摄像头
    cv2.destroyAllWindows()

    # 关闭日志处理器
    if app_state.log_file_handler:
        logger = logging.getLogger()
        try:
            # 确保在关闭前移除 handler
            logger.removeHandler(app_state.log_file_handler)
            app_state.log_file_handler.close()
            logging.info("日志文件处理器已关闭。")
        except Exception as e:
            # 在关闭时可能发生错误，记录但不中断退出
            print(f"关闭日志处理器时出错: {e}")

    logging.info("程序正常结束")
    print("程序已退出。")

if __name__ == "__main__":
    main()
