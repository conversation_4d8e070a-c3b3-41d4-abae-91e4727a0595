"""
最基本的UI测试 - 验证主UI是否能正常显示
"""
import cv2
import numpy as np
import time

def basic_ui_test():
    """最基本的UI测试"""
    print("=== 基本UI测试 ===")
    
    # 创建主窗口
    window_name = "Basic UI Test"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, 640, 480)
    
    print("窗口已创建，按 ESC 键退出...")
    
    frame_count = 0
    start_time = time.time()
    
    try:
        while True:
            # 创建一个简单的测试帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 添加一些动态内容
            center_x = int(320 + 200 * np.sin(frame_count * 0.02))
            center_y = int(240 + 200 * np.cos(frame_count * 0.02))
            
            # 绘制移动的圆
            cv2.circle(frame, (center_x, center_y), 30, (0, 255, 0), -1)
            
            # 绘制文字
            cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # 显示帧
            cv2.imshow(window_name, frame)
            
            # 处理按键
            key = cv2.waitKey(30) & 0xFF
            if key == 27:  # ESC
                break
            
            frame_count += 1
            
            # 每秒输出一次状态
            if frame_count % 33 == 0:
                elapsed = time.time() - start_time
                fps = frame_count / elapsed
                print(f"运行时间: {elapsed:.1f}s, 帧数: {frame_count}, FPS: {fps:.1f}")
    
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    
    finally:
        cv2.destroyAllWindows()
        print("测试结束")

if __name__ == "__main__":
    basic_ui_test()