"""
UI Components Package
包含UI相关的所有组件：绘制、事件处理、模式逻辑等
"""

# 导入主要组件，提供统一的访问接口
from .ui_drawing import draw_rois, draw_hud
from .ui_events import setup_mouse_callback, set_shared_state, get_key, toggle_window_topmost
from .ui_modes import _run_camera_settings_mode, _run_calibration_mode, _run_detection_mode

# 定义包的公共接口
__all__ = [
    'draw_rois', 'draw_hud',
    'setup_mouse_callback', 'set_shared_state', 'get_key', 'toggle_window_topmost',
    '_run_camera_settings_mode', '_run_calibration_mode', '_run_detection_mode'
]