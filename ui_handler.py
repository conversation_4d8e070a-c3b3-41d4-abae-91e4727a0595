"""
UI Handler - 重构后的主协调器
负责协调各个UI组件，提供统一的接口
"""

import cv2
import numpy as np
import time
import logging
from app_state import AppState
from constants import *

# 导入拆分后的组件
from ui_components.ui_drawing import draw_rois, draw_hud
from ui_components.ui_events import setup_mouse_callback, set_shared_state
from ui_components.ui_modes import _run_camera_settings_mode, _run_calibration_mode, _run_detection_mode
from roi_fine_tune import draw_fine_tune_info
import async_task_manager

# 保持向后兼容性的函数映射
# 这些函数现在在其他模块中定义，这里重新导出以保持兼容性
__all__ = [
    'setup_mouse_callback', 'set_shared_state', 'get_key', 'toggle_window_topmost',
    'draw_rois', 'draw_hud', 'process_core_logic', 'prepare_display_frame', 'process_ui_and_logic'
]

# 从ui_events导入一些函数以保持兼容性
from ui_components.ui_events import get_key, toggle_window_topmost


def process_core_logic(app_state: AppState):
    """核心处理函数，只处理采集和检测逻辑，不包含显示"""

    # --- 更新处理 FPS ---
    curr_time = time.time()
    time_diff = curr_time - app_state.prev_time
    if time_diff > 0:
        app_state.fps = 1.0 / time_diff
    app_state.prev_time = curr_time

    # --- 定期清理异步任务 ---
    if curr_time - app_state.last_task_cleanup_time > 60.0:  # 每60秒清理一次
        task_manager = async_task_manager.get_task_manager()
        task_manager.cleanup_old_tasks()
        app_state.last_task_cleanup_time = curr_time

    # --- 根据模式执行核心逻辑 ---
    if app_state.current_mode == MODE_CAMERA_SETTINGS:
        app_state.prompt_message = ""
        app_state.status_message = ""
        _run_camera_settings_mode(app_state)
    elif app_state.current_mode == MODE_CALIBRATION:
        app_state.prompt_message = ""
        app_state.status_message = ""
        _run_calibration_mode(app_state)
    elif app_state.current_mode == MODE_DETECTION:
        app_state.prompt_message = ""
        app_state.status_message = ""
        _run_detection_mode(app_state)
    else:
        print(f"错误: 未知模式 {app_state.current_mode}")
        app_state.running = False

    return True


def prepare_display_frame(app_state: AppState):
    """准备显示帧，包含所有绘制元素"""
    if app_state.display_frame is not None and isinstance(app_state.display_frame, np.ndarray):
        # 调试信息（默认关闭，避免每帧打印造成抖动）
        # logging.debug(f"准备显示帧: {app_state.display_frame.shape}")

        # 绘制 ROIs
        draw_rois(app_state)
        # 绘制 HUD (状态信息、提示等)
        draw_hud(app_state)
        # 绘制通用微调信息（右上角步长提示）
        draw_fine_tune_info(app_state, app_state.display_frame)

        return app_state.display_frame.copy()
    else:
        # 创建错误信息帧
        print(f"警告: display_frame 无效: {type(app_state.display_frame)}")
        error_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.putText(error_frame, "Error: display_frame is invalid", (50, 240),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        return error_frame


def process_ui_and_logic(app_state: AppState):
    """兼容性函数，保持原有接口（单线程模式）"""
    process_core_logic(app_state)

    # 准备并显示帧
    display_frame = prepare_display_frame(app_state)
    cv2.imshow(MAIN_WINDOW, display_frame)