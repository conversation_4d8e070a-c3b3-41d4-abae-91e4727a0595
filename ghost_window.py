"""
幽灵窗口管理模块 - 创建一个2x2像素的透明窗口
即使在主界面最小化时也保持对操作系统的可见性
"""
import cv2
import numpy as np
import threading
import time
import logging
from constants import *

class GhostWindow:
    """2x2像素的幽灵窗口管理器"""
    
    def __init__(self, window_name="GhostWindow"):
        self.window_name = window_name
        self.is_running = False
        self.thread = None
        self.window_created = False
        self.frame_updated = False  # 标记帧是否已更新
        
        # 创建2x2像素的透明图像
        self.ghost_frame = np.ones((2, 2, 3), dtype=np.uint8) * 128  # 灰色背景
        
        # 窗口位置和样式
        self.window_x = 50
        self.window_y = 50
        
        # 添加延迟，避免与主窗口冲突
        self.init_delay = 1.0  # 1秒延迟
        
    def create_window(self):
        """创建幽灵窗口"""
        try:
            # 创建窗口
            cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
            
            # 设置窗口大小为2x2像素
            cv2.resizeWindow(self.window_name, 2, 2)
            
            # 移动窗口到指定位置
            cv2.moveWindow(self.window_name, self.window_x, self.window_y)
            
            # 设置窗口属性（尝试保持置顶）
            try:
                # 在Windows上尝试设置窗口属性
                import ctypes
                hwnd = cv2.getWindowProperty(self.window_name, cv2.WND_PROP_VISIBLE)
                if hwnd > 0:
                    # 尝试获取窗口句柄并设置属性
                    user32 = ctypes.windll.user32
                    # 设置窗口为工具窗口（不在任务栏显示）
                    # GWL_EXSTYLE = -20
                    # WS_EX_TOOLWINDOW = 0x00000080
                    user32.SetWindowLongW(hwnd, -20, 0x00000080)
            except Exception as e:
                logging.debug(f"无法设置窗口属性: {e}")
            
            self.window_created = True
            logging.info(f"幽灵窗口 '{self.window_name}' 创建成功")
            
        except Exception as e:
            logging.error(f"创建幽灵窗口失败: {e}")
            self.window_created = False
    
    def update_window_content(self):
        """仅更新窗口内容，不调用waitKey"""
        if not self.window_created:
            return
            
        try:
            # 显示2x2像素图像
            cv2.imshow(self.window_name, self.ghost_frame)
            self.frame_updated = True
            
        except Exception as e:
            logging.error(f"更新幽灵窗口内容失败: {e}")
    
    def _run_loop(self):
        """幽灵窗口运行循环"""
        logging.info("幽灵窗口线程启动")
        
        # 等待延迟时间后再创建窗口
        if self.init_delay > 0:
            time.sleep(self.init_delay)
        
        # 创建窗口
        self.create_window()
        
        update_counter = 0
        while self.is_running:
            try:
                # 降低更新频率，每500ms更新一次
                if update_counter % 5 == 0:
                    self.update_window_content()
                
                update_counter += 1
                time.sleep(0.1)  # 基础循环间隔100ms
                
            except Exception as e:
                logging.error(f"幽灵窗口运行错误: {e}")
                time.sleep(1)  # 出错后等待1秒再重试
        
        logging.info("幽灵窗口线程停止")
    
    def update_in_main_thread(self):
        """在主线程中调用waitKey以保持窗口响应"""
        if self.window_created and self.frame_updated:
            try:
                # 使用极短的waitKey时间，减少对主UI的影响
                cv2.waitKey(1)
                self.frame_updated = False
            except Exception as e:
                logging.error(f"幽灵窗口waitKey失败: {e}")
    
    def start(self):
        """启动幽灵窗口"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # 创建窗口
        self.create_window()
        
        # 启动更新线程
        self.thread = threading.Thread(target=self._run_loop, daemon=True)
        self.thread.start()
        
        logging.info("幽灵窗口已启动")
    
    def stop(self):
        """停止幽灵窗口"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)
        
        # 销毁窗口
        if self.window_created:
            try:
                cv2.destroyWindow(self.window_name)
                logging.info("幽灵窗口已销毁")
            except Exception as e:
                logging.error(f"销毁幽灵窗口失败: {e}")
        
        self.window_created = False
        logging.info("幽灵窗口已停止")
    
    def set_position(self, x, y):
        """设置窗口位置"""
        self.window_x = x
        self.window_y = y
        if self.window_created:
            try:
                cv2.moveWindow(self.window_name, x, y)
            except Exception as e:
                logging.error(f"移动幽灵窗口失败: {e}")
    
    def set_color(self, bgr_color):
        """设置窗口颜色"""
        self.ghost_frame = np.full((2, 2, 3), bgr_color, dtype=np.uint8)
    
    def is_visible(self):
        """检查窗口是否可见"""
        if not self.window_created:
            return False
        try:
            visible = cv2.getWindowProperty(self.window_name, cv2.WND_PROP_VISIBLE)
            return visible > 0
        except:
            return False

# 全局幽灵窗口实例
ghost_window = None

def initialize_ghost_window():
    """初始化全局幽灵窗口"""
    global ghost_window
    if ghost_window is None:
        ghost_window = GhostWindow()
        ghost_window.start()
        logging.info("全局幽灵窗口已初始化")
    return ghost_window

def shutdown_ghost_window():
    """关闭全局幽灵窗口"""
    global ghost_window
    if ghost_window is not None:
        ghost_window.stop()
        ghost_window = None
        logging.info("全局幽灵窗口已关闭")

def get_ghost_window():
    """获取全局幽灵窗口实例"""
    global ghost_window
    return ghost_window

def update_ghost_window_in_main_thread():
    """在主线程中更新幽灵窗口（从主循环调用）"""
    global ghost_window
    if ghost_window is not None:
        ghost_window.update_in_main_thread()