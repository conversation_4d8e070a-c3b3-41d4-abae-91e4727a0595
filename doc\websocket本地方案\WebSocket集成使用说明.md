# 视觉检测WebSocket集成使用说明

## 📋 功能概述

本次更新为视觉检测程序集成了WebSocket服务器，实现与网页端的实时通信。当检测到数码管显示"88"时，会自动开始50秒的LED状态数据采集，并将倒计时状态实时推送到网页端。

## 🚀 新增功能

### 1. WebSocket实时通信
- **端口**: 8766
- **协议**: WebSocket (ws://localhost:8766)
- **消息格式**: JSON

### 2. 自动检测触发
- 检测到数码管显示"88"时自动开始倒计时
- 50秒LED状态数据采集过程
- 实时状态推送到网页端

### 3. 网页端倒计时显示
- 圆形进度条显示倒计时
- 实时状态消息更新
- WebSocket连接状态指示
- 检测开始时间显示

## 🔧 安装和配置

### 1. 安装依赖
```bash
pip install -r requirements_websocket.txt
```

### 2. 启动程序
正常启动视觉检测程序即可，WebSocket服务器会自动启动：
```bash
python main.py
```

### 3. 验证连接
- 启动程序后，WebSocket服务器会在8766端口启动
- 打开网页端CPU控制器V页面
- 查看"视觉检测"按钮附近的连接状态指示器

## 📱 使用流程

### 1. 系统启动
1. 启动本地代理服务器（端口5000）
2. 启动视觉检测程序（自动启动WebSocket服务器端口8766）
3. 启动Flask Web服务器（端口3308）
4. 打开网页，自动连接WebSocket服务器

### 2. 检测流程
1. 在网页端连接设备
2. 本地视觉检测程序检测到数码管显示"88"
3. 自动开始50秒LED状态数据采集
4. 网页端实时显示倒计时和状态
5. 检测完成后通知网页端

### 3. 状态显示
- **连接状态**: 绿色圆点表示已连接，红色表示未连接
- **倒计时圆圈**: 显示剩余秒数和进度
- **状态消息**: 显示当前操作详情
- **开始时间**: 显示检测开始的时间

## 🎨 界面说明

### 连接状态指示器
```
🟢 已连接本地程序  - WebSocket连接正常
🔴 未连接本地程序  - WebSocket连接断开
```

### 倒计时显示组件
- **圆形进度条**: 显示倒计时进度
- **倒计时数字**: 显示剩余秒数
- **状态消息**: 显示当前操作状态
- **开始时间**: 显示检测开始时间

### 按钮状态
- **视觉检测**: 正常状态，可点击开始检测
- **检测中...**: 正在进行检测，按钮禁用
- **禁用状态**: 设备未连接或检测进行中

## 🔄 消息协议

### 浏览器 → 本地程序
```json
{
  "type": "query_status",
  "data": {
    "timestamp": "2025-01-20 10:30:00"
  }
}
```

### 本地程序 → 浏览器
```json
{
  "type": "status_update",
  "data": {
    "state": "LOGGING",
    "countdown": 45,
    "total_duration": 50,
    "progress": 10,
    "message": "正在记录LED状态数据...",
    "is_active": true,
    "start_time": "10:30:00",
    "timestamp": "2025-01-20 10:30:45"
  }
}
```

## 🛠️ 技术特点

### 优势
- **零延迟**: 本地通信，毫秒级响应
- **用户隔离**: 每个用户独立的通信环境
- **自动重连**: 网络断开时自动恢复连接
- **状态同步**: 实时同步检测状态
- **错误处理**: 完善的异常处理机制

### 性能指标
- **连接建立时间**: < 1秒
- **消息传输延迟**: < 100毫秒
- **状态更新频率**: 每秒1次
- **重连间隔**: 10秒
- **资源占用**: 低CPU和内存占用

## 🐛 故障排除

### 1. WebSocket连接失败
**问题**: 网页端显示"未连接本地程序"
**解决方案**:
- 确认视觉检测程序已启动
- 检查8766端口是否被占用
- 查看程序日志中的WebSocket启动信息

### 2. 倒计时不显示
**问题**: 检测到"88"但网页端无倒计时
**解决方案**:
- 检查WebSocket连接状态
- 查看浏览器控制台错误信息
- 确认数码管检测功能正常

### 3. 连接频繁断开
**问题**: WebSocket连接不稳定
**解决方案**:
- 检查网络环境
- 确认防火墙设置
- 查看程序日志中的错误信息

## 📝 日志说明

### WebSocket相关日志
- `WEBSOCKET`: WebSocket服务器启动/关闭
- `VISUAL_WS`: 视觉检测状态更新
- `VISUAL_SYSTEM`: 视觉检测系统消息

### 示例日志
```
[INFO] WEBSOCKET: WebSocket服务器已启动: ws://localhost:8766
[INFO] WEBSOCKET: WebSocket客户端连接: 127.0.0.1:xxxxx
[INFO] VISUAL_WS: 检测到数码管显示'88'，开始LED状态数据采集
[INFO] VISUAL_WS: 视觉检测倒计时: 45秒
[SUCCESS] VISUAL_WS: 视觉检测完成
```

## 🔒 安全考虑

- **本地通信**: 所有通信都在本地环境内进行
- **端口隔离**: 使用非标准端口避免冲突
- **状态验证**: 对接收的状态数据进行验证
- **错误隔离**: 单个连接错误不影响其他功能

## 📞 技术支持

如遇到问题，请：
1. 查看程序日志文件
2. 检查浏览器控制台错误
3. 确认网络和端口配置
4. 联系技术支持团队
