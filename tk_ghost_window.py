"""
简化版幽灵窗口 - 使用Tkinter创建，避免与OpenCV冲突
"""
import tkinter as tk
import time
import logging
import threading
import sys

class TkGhostWindow:
    """使用Tkinter的幽灵窗口实现"""
    
    def __init__(self, window_title="GhostWindow"):
        self.window_title = window_title
        self.is_running = False
        self.thread = None
        self.root = None
        self.window = None
        
        # 窗口设置
        self.window_size = 1
        self.window_x = 1
        self.window_y = 1
        self.window_color = "#808080"  # 灰色
        
    def create_window(self):
        """创建幽灵窗口"""
        try:
            # 创建Tk根窗口
            self.root = tk.Tk()
            self.root.withdraw()  # 隐藏主窗口
            
            # 创建幽灵窗口
            self.window = tk.Toplevel(self.root)
            self.window.title(self.window_title)
            self.window.geometry(f"{self.window_size}x{self.window_size}+{self.window_x}+{self.window_y}")
            
            # 设置窗口属性
            self.window.overrideredirect(True)  # 无边框
            self.window.attributes('-topmost', True)  # 置顶
            self.window.attributes('-alpha', 0.8)  # 半透明
            
            # 创建标签作为背景
            label = tk.Label(self.window, bg=self.window_color, width=self.window_size, height=self.window_size)
            label.pack()
            
            # 绑定关闭事件
            self.window.protocol("WM_DELETE_WINDOW", self.on_window_close)
            
            logging.info(f"Tkinter幽灵窗口 '{self.window_title}' 创建成功 (1x1@1,1)")
            
        except Exception as e:
            logging.error(f"创建Tkinter幽灵窗口失败: {e}")
            self.window = None
    
    def update_window(self):
        """更新窗口"""
        if self.window is None or self.root is None:
            return
            
        try:
            # 更新窗口位置
            self.window.geometry(f"{self.window_size}x{self.window_size}+{self.window_x}+{self.window_y}")
            
            # 更新窗口颜色
            for widget in self.window.winfo_children():
                if isinstance(widget, tk.Label):
                    widget.config(bg=self.window_color)
            
            # 处理Tkinter事件
            self.root.update()
            
        except Exception as e:
            logging.error(f"更新Tkinter幽灵窗口失败: {e}")
    
    def on_window_close(self):
        """窗口关闭事件"""
        logging.info("Tkinter幽灵窗口被用户关闭")
        self.window.destroy()
        self.window = None
    
    def _run_loop(self):
        """幽灵窗口运行循环"""
        logging.info("Tkinter幽灵窗口线程启动")
        
        # 等待延迟时间后再创建窗口
        time.sleep(2.0)  # 增加延迟确保主窗口先创建
        
        # 创建窗口
        self.create_window()
        
        if self.window is None:
            logging.error("无法创建Tkinter幽灵窗口")
            return
        
        # 运行窗口更新循环
        try:
            while self.is_running and self.window is not None:
                self.update_window()
                time.sleep(1.0)  # 每秒更新一次
                
        except Exception as e:
            logging.error(f"Tkinter幽灵窗口运行错误: {e}")
        
        # 清理
        if self.window:
            try:
                self.window.destroy()
            except:
                pass
        
        if self.root:
            try:
                self.root.destroy()
            except:
                pass
        
        logging.info("Tkinter幽灵窗口线程停止")
    
    def start(self):
        """启动幽灵窗口"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # 启动线程
        self.thread = threading.Thread(target=self._run_loop, daemon=True)
        self.thread.start()
        
        logging.info("Tkinter幽灵窗口已启动")
    
    def stop(self):
        """停止幽灵窗口"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 关闭窗口
        if self.window:
            try:
                self.window.destroy()
            except Exception as e:
                logging.error(f"关闭Tkinter幽灵窗口失败: {e}")
        
        if self.root:
            try:
                self.root.destroy()
            except Exception as e:
                logging.error(f"关闭Tkinter根窗口失败: {e}")
        
        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)
        
        self.window = None
        self.root = None
        logging.info("Tkinter幽灵窗口已停止")
    
    def set_position(self, x, y):
        """设置窗口位置"""
        self.window_x = x
        self.window_y = y
        if self.window:
            try:
                self.window.geometry(f"{self.window_size}x{self.window_size}+{x}+{y}")
            except Exception as e:
                logging.error(f"移动Tkinter幽灵窗口失败: {e}")
    
    def set_color(self, rgb_color):
        """设置窗口颜色"""
        # 将RGB转换为十六进制颜色
        r, g, b = rgb_color
        self.window_color = f"#{r:02x}{g:02x}{b:02x}"
        if self.window:
            try:
                for widget in self.window.winfo_children():
                    if isinstance(widget, tk.Label):
                        widget.config(bg=self.window_color)
            except Exception as e:
                logging.error(f"设置Tkinter幽灵窗口颜色失败: {e}")

# 全局Tkinter幽灵窗口实例
tk_ghost_window = None

def initialize_tk_ghost_window():
    """初始化全局Tkinter幽灵窗口"""
    global tk_ghost_window
    if tk_ghost_window is None:
        tk_ghost_window = TkGhostWindow()
        tk_ghost_window.start()
        logging.info("全局Tkinter幽灵窗口已初始化")
    return tk_ghost_window

def shutdown_tk_ghost_window():
    """关闭全局Tkinter幽灵窗口"""
    global tk_ghost_window
    if tk_ghost_window is not None:
        tk_ghost_window.stop()
        tk_ghost_window = None
        logging.info("全局Tkinter幽灵窗口已关闭")

def get_tk_ghost_window():
    """获取全局Tkinter幽灵窗口实例"""
    global tk_ghost_window
    return tk_ghost_window