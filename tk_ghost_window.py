"""
简化版幽灵窗口 - 使用原生OpenCV创建，避免GUI冲突
"""
import cv2
import numpy as np
import time
import logging
import threading
import sys

class TkGhostWindow:
    """使用原生OpenCV的幽灵窗口实现"""

    def __init__(self, window_title="GhostWindow"):
        self.window_title = window_title
        self.is_running = False
        self.thread = None
        self.window_created = False

        # 窗口设置
        self.window_size = 1
        self.window_x = 1
        self.window_y = 1
        self.window_color = (128, 128, 128)  # BGR格式：灰色

        # 创建1x1像素的图像
        self.ghost_frame = np.full((1, 1, 3), self.window_color, dtype=np.uint8)
        
    def create_window(self):
        """创建幽灵窗口"""
        try:
            # 创建OpenCV窗口
            cv2.namedWindow(self.window_title, cv2.WINDOW_NORMAL)

            # 设置窗口大小为1x1像素
            cv2.resizeWindow(self.window_title, self.window_size, self.window_size)

            # 移动窗口到指定位置
            cv2.moveWindow(self.window_title, self.window_x, self.window_y)

            # 显示初始内容
            cv2.imshow(self.window_title, self.ghost_frame)

            # 尝试设置窗口置顶（Windows）
            self._set_window_topmost()

            self.window_created = True
            logging.info(f"OpenCV幽灵窗口 '{self.window_title}' 创建成功 (1x1@{self.window_x},{self.window_y})")

        except Exception as e:
            logging.error(f"创建OpenCV幽灵窗口失败: {e}")
            self.window_created = False

    def _set_window_topmost(self):
        """设置OpenCV窗口置顶（Windows）"""
        try:
            import ctypes
            hwnd = cv2.getWindowProperty(self.window_title, cv2.WND_PROP_VISIBLE)
            if hwnd > 0:
                HWND_TOPMOST = -1
                SWP_NOMOVE = 0x0002
                SWP_NOSIZE = 0x0001
                ctypes.windll.user32.SetWindowPos(
                    int(hwnd), HWND_TOPMOST, 0, 0, 0, 0,
                    SWP_NOMOVE | SWP_NOSIZE
                )
        except Exception as e:
            logging.debug(f"设置窗口置顶失败（可能不是Windows系统）: {e}")

    def update_window(self):
        """更新窗口 - 关键：不调用waitKey()避免冲突"""
        if not self.window_created:
            return

        try:
            # 更新窗口位置
            cv2.moveWindow(self.window_title, self.window_x, self.window_y)

            # 更新显示内容
            cv2.imshow(self.window_title, self.ghost_frame)

            # 关键：不调用cv2.waitKey()，避免与主线程冲突

        except Exception as e:
            logging.error(f"更新OpenCV幽灵窗口失败: {e}")
    
    def _run_loop(self):
        """幽灵窗口运行循环"""
        logging.info("OpenCV幽灵窗口线程启动")

        # 等待延迟时间后再创建窗口
        time.sleep(2.0)  # 增加延迟确保主窗口先创建

        # 创建窗口
        self.create_window()

        if not self.window_created:
            logging.error("无法创建OpenCV幽灵窗口")
            return

        # 运行窗口更新循环
        try:
            while self.is_running and self.window_created:
                self.update_window()
                time.sleep(1.0)  # 每秒更新一次

        except Exception as e:
            logging.error(f"OpenCV幽灵窗口运行错误: {e}")

        # 清理
        if self.window_created:
            try:
                cv2.destroyWindow(self.window_title)
            except:
                pass

        logging.info("OpenCV幽灵窗口线程停止")
    
    def start(self):
        """启动幽灵窗口"""
        if self.is_running:
            return

        self.is_running = True

        # 启动线程
        self.thread = threading.Thread(target=self._run_loop, daemon=True)
        self.thread.start()

        logging.info("OpenCV幽灵窗口已启动")

    def stop(self):
        """停止幽灵窗口"""
        if not self.is_running:
            return

        self.is_running = False

        # 关闭窗口
        if self.window_created:
            try:
                cv2.destroyWindow(self.window_title)
            except Exception as e:
                logging.error(f"关闭OpenCV幽灵窗口失败: {e}")

        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)

        self.window_created = False
        logging.info("OpenCV幽灵窗口已停止")
    
    def set_position(self, x, y):
        """设置窗口位置"""
        self.window_x = x
        self.window_y = y
        if self.window_created:
            try:
                cv2.moveWindow(self.window_title, x, y)
            except Exception as e:
                logging.error(f"移动OpenCV幽灵窗口失败: {e}")

    def set_color(self, rgb_color):
        """设置窗口颜色"""
        # 将RGB转换为BGR（OpenCV使用BGR格式）
        r, g, b = rgb_color
        self.window_color = (b, g, r)  # BGR格式
        self.ghost_frame = np.full((1, 1, 3), self.window_color, dtype=np.uint8)

        if self.window_created:
            try:
                cv2.imshow(self.window_title, self.ghost_frame)
            except Exception as e:
                logging.error(f"设置OpenCV幽灵窗口颜色失败: {e}")

# 全局OpenCV幽灵窗口实例
tk_ghost_window = None

def initialize_tk_ghost_window():
    """初始化全局OpenCV幽灵窗口"""
    global tk_ghost_window
    if tk_ghost_window is None:
        tk_ghost_window = TkGhostWindow()
        tk_ghost_window.start()
        logging.info("全局OpenCV幽灵窗口已初始化")
    return tk_ghost_window

def shutdown_tk_ghost_window():
    """关闭全局OpenCV幽灵窗口"""
    global tk_ghost_window
    if tk_ghost_window is not None:
        tk_ghost_window.stop()
        tk_ghost_window = None
        logging.info("全局OpenCV幽灵窗口已关闭")

def get_tk_ghost_window():
    """获取全局OpenCV幽灵窗口实例"""
    global tk_ghost_window
    return tk_ghost_window