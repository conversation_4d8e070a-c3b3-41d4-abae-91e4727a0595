# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an industrial computer vision application for LED and digit tube detection (LED与数码管视觉检测系统). The system uses OpenCV for real-time camera processing and implements a sophisticated multi-threaded architecture for high-performance detection of 33 green LEDs + 2 red LEDs and seven-segment digit displays.

## Development Commands

### Running the Application
```bash
# Install dependencies
pip install -r requirements.txt

# Run the main application
python main.py
```

### Testing
The project includes comprehensive test scripts in the `testscripts/` directory:

```bash
# Run WebSocket integration test
python testscripts/test_main_websocket.py

# Test specific functionality
python testscripts/test_led_detailed_status.py
python testscripts/test_g33_functionality.py
python testscripts/test_digit_display_push.py

# Test network components
python testscripts/test_websocket.py
python testscripts/test_network_timeout.py
```

## Architecture Overview

### Core Architecture
- **Main Thread**: GUI display using OpenCV (cv2.imshow, cv2.waitKey)
- **Capture Thread**: Camera frame acquisition
- **Processing Thread**: LED and digit detection algorithms
- **Display Thread**: Optional (currently disabled, set USE_DISPLAY_THREAD = False in main.py)
- **Ghost Window Thread**: Independent 2x2 pixel Tkinter window for OS visibility

### UI Modular Architecture (Refactored 2025-08)
The UI was refactored from a monolithic 1955-line ui_handler.py into a modular architecture:

- **ui_handler.py** (96 lines): Main coordinator, maintains backward compatibility
- **ui_components/**: Modular package with specialized components:
  - `ui_drawing.py`: All drawing functions (ROIs, HUD, overlays)
  - `ui_events.py`: Event handling (keyboard, mouse, window management)
  - `ui_modes.py`: Mode-specific logic (camera, calibration, detection)
  - `analysis_state_machine.py`: '88' analysis state machine (M12→analysis→M10→M13→cleanup)

### Key Components
- **app_state.py**: Global state management with thread-safe communication
- **constants.py**: All configuration constants and thresholds
- **config_manager.py**: JSON configuration management
- **camera_manager.py**: Camera initialization and parameter management
- **led_detector.py**: LED detection with special G33 handling
- **digit_detector.py**: Seven-segment display recognition
- **simple_websocket_server.py**: Real-time WebSocket server
- **cpu_communicator.py**: Serial communication with external controllers
- **tk_ghost_window.py**: 2x2 pixel window implementation

## Configuration System

The application uses `combined_config.json` for runtime configuration with:
- Camera settings (resolution, exposure, brightness)
- LED detection thresholds and ROI coordinates
- Special LED configurations (G33 has independent threshold)
- Digit detection brightness thresholds
- Sample data for automatic threshold calculation

## Key Features

### LED Detection
- 33 green LEDs + 2 red LEDs
- Special G33 LED with independent threshold
- Brightest 10% pixel sampling algorithm
- Historical state smoothing for stability

### Digit Tube Recognition
- Seven-segment display support (2 digits, 7 segments each)
- Character mapping for 0-9, A-F
- Brightness-based threshold detection

### Real-time Communication
- WebSocket server for status notifications
- CPU serial communication
- Intelligent log filtering

### Ghost Window
- 2x2 pixel Tkinter window
- Ensures OS visibility when main UI is minimized
- Dynamic color changes and position movement

## Development Notes

### Threading Model
- Uses Queue for thread-safe communication
- Non-blocking queues with maxsize=1 (drop old, keep new)
- High-resolution timer (1ms precision on Windows)

### Performance Considerations
- Frame dropping for real-time performance
- Memory-efficient queue management
- Asynchronous task processing for logging and communication

### UI Development
- All UI rendering must happen on main thread (OpenCV limitation)
- Use ui_handler.py as the main entry point for UI operations
- Modular components allow for focused development on specific areas

### Configuration Management
- Configuration changes should be made through config_manager.py
- Use constants.py for any new constant definitions
- Maintain backward compatibility with existing configurations

## Testing Guidelines

- Test individual components using scripts in testscripts/
- WebSocket tests can run without camera hardware
- LED/detection tests require proper camera setup
- All tests should be non-destructive to production configuration

## Important Implementation Details

1. **Windows Timer**: The application calls `high_res_timer.enable_1ms()` for 1ms precision
2. **Camera Backend**: Uses DSHOW/MSMF backends for better compatibility
3. **Logging**: Uses strict format for log analysis compatibility
4. **State Management**: AppState class centralizes all shared state
5. **Modular UI**: When making UI changes, respect the modular architecture boundaries